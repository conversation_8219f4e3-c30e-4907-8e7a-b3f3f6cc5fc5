import { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, MapPin, Search, X, Loader2 } from 'lucide-react';
import { useLocations } from '../hooks/useLocations';
import { LocationService } from '../services/locationService';
import type { LocationHierarchy } from '../types/location.types';

interface LocationAccordionProps {
  selectedItems?: Set<string>;
  onSelectionChange?: (selectedItems: Set<string>) => void;
  title?: string;
  subtitle?: string;
  allowMultiple?: boolean;
  initialSelectedKeys?: string[];
  disabled?: boolean;
}

const LocationAccordion = ({
  selectedItems: controlledSelectedItems,
  onSelectionChange,
  title = 'Select UK Locations',
  subtitle,
  allowMultiple = true,
  initialSelectedKeys = [],
  disabled = false
}: LocationAccordionProps) => {
  // State management
  const [expandedItems, setExpandedItems] = useState(new Set<string>());
  const [internalSelectedItems, setInternalSelectedItems] = useState(new Set<string>(initialSelectedKeys));
  const [searchTerm, setSearchTerm] = useState('');
  
  // Use controlled or internal state
  const selectedItems = controlledSelectedItems || internalSelectedItems;
  
  // Load location data from database
  const { locationHierarchy, loading, error } = useLocations();

  // Handle selection change
  const handleSelectionChange = (newSelection: Set<string>) => {
    if (disabled) return;
    
    if (onSelectionChange) {
      onSelectionChange(newSelection);
    } else {
      setInternalSelectedItems(newSelection);
    }
  };

  const toggleExpanded = (key: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedItems(newExpanded);
  };

  const toggleSelected = (key: string) => {
    if (disabled) return;
    
    const newSelected = new Set(selectedItems);
    if (newSelected.has(key)) {
      newSelected.delete(key);
    } else {
      if (!allowMultiple) {
        newSelected.clear();
      }
      newSelected.add(key);
    }
    handleSelectionChange(newSelected);
  };

  const isSelected = (key: string) => selectedItems.has(key);
  const isExpanded = (key: string) => expandedItems.has(key);

  const getSelectedCount = () => selectedItems.size;

  const clearSearch = () => setSearchTerm('');
  const clearAllSelections = () => handleSelectionChange(new Set());

  const filteredLocationData = () => {
    if (!searchTerm) return locationHierarchy;

    const filtered: LocationHierarchy = {};
    const newExpanded = new Set(expandedItems);
    
    Object.entries(locationHierarchy).forEach(([countryKey, country]) => {
      const filteredRegions: typeof country.regions = {};
      Object.entries(country.regions).forEach(([regionKey, region]) => {
        const filteredCounties = region.counties.filter(county =>
          county.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        if (filteredCounties.length > 0) {
          filteredRegions[regionKey] = {
            ...region,
            counties: filteredCounties
          };
          // Auto-expand when there are matches
          newExpanded.add(countryKey);
          newExpanded.add(regionKey);
        }
      });
      if (Object.keys(filteredRegions).length > 0) {
        filtered[countryKey] = {
          ...country,
          regions: filteredRegions
        };
      }
    });
    
    // Update expanded items if there's a search
    if (searchTerm && newExpanded.size !== expandedItems.size) {
      setExpandedItems(newExpanded);
    }
    
    return filtered;
  };

  const data = filteredLocationData();

  const generateSelectedItemDisplay = (item: string) => {
    const parts = item.split('::');
    const displayName = parts[2]?.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ') || item;
    return displayName;
  };

  // Calculate total locations for display
  const totalLocations = Object.values(locationHierarchy).reduce((total, country) => 
    total + Object.values(country.regions).reduce((regionTotal, region) => 
      regionTotal + region.counties.length, 0), 0);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading locations...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white">
        <div className="text-center py-8 text-red-600">
          <p>Error loading locations: {error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="mt-2 text-blue-600 hover:text-blue-800 underline"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="mb-6">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-sm text-gray-600 mb-4">
          {subtitle || `${getSelectedCount()} location${getSelectedCount() !== 1 ? 's' : ''} selected`}
        </p>
        
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search counties..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            disabled={disabled}
            className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
          />
          {searchTerm && (
            <button
              onClick={clearSearch}
              disabled={disabled}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 disabled:cursor-not-allowed"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      {/* Selected Items Display */}
      {getSelectedCount() > 0 && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-blue-900">
              Selected Locations ({getSelectedCount()})
            </h3>
            <button
              onClick={clearAllSelections}
              disabled={disabled}
              className="text-sm text-blue-600 hover:text-blue-800 underline disabled:cursor-not-allowed disabled:opacity-60"
            >
              Clear All
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            {Array.from(selectedItems).map((item) => (
              <div
                key={item}
                className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
              >
                <span>{generateSelectedItemDisplay(item)}</span>
                {!disabled && (
                  <button
                    onClick={() => toggleSelected(item)}
                    className="ml-2 text-blue-600 hover:text-blue-800"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="border border-gray-200 rounded-lg overflow-hidden">
        {Object.entries(data).map(([countryKey, country]) => (
          <div key={countryKey} className="border-b border-gray-200 last:border-b-0">
            {/* Country Level */}
            <div 
              className={`flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer transition-colors bg-gray-25 ${
                disabled ? 'cursor-not-allowed opacity-60' : ''
              }`}
              onClick={() => !disabled && toggleExpanded(countryKey)}
            >
              <div className="flex items-center space-x-3">
                {isExpanded(countryKey) ? 
                  <ChevronDown className="w-5 h-5 text-gray-500" /> : 
                  <ChevronRight className="w-5 h-5 text-gray-500" />
                }
                <span className="text-xl">📍</span>
                <span className="font-bold text-gray-900 text-lg">{country.name}</span>
                <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                  {Object.values(country.regions).reduce((total, region) => total + region.counties.length, 0)} counties
                </span>
              </div>
            </div>

            {/* Regions Level */}
            {isExpanded(countryKey) && (
              <div className="bg-gray-50">
                {Object.entries(country.regions).map(([regionKey, region]) => (
                  <div key={regionKey} className="border-t border-gray-200 first:border-t-0">
                    <div 
                      className={`flex items-center justify-between p-4 pl-12 hover:bg-gray-100 cursor-pointer transition-colors ${
                        disabled ? 'cursor-not-allowed' : ''
                      }`}
                      onClick={() => !disabled && toggleExpanded(regionKey)}
                    >
                      <div className="flex items-center space-x-3">
                        {isExpanded(regionKey) ? 
                          <ChevronDown className="w-4 h-4 text-gray-500" /> : 
                          <ChevronRight className="w-4 h-4 text-gray-500" />
                        }
                        <MapPin className="w-4 h-4 text-blue-600" />
                        <span className="font-semibold text-gray-800">{region.name}</span>
                        <span className="text-xs text-gray-500 bg-gray-300 px-2 py-1 rounded-full">
                          {region.counties.length} counties
                        </span>
                      </div>
                    </div>

                    {/* Counties Level */}
                    {isExpanded(regionKey) && (
                      <div className="bg-white">
                        {region.counties.map((county) => {
                          const countyKey = `${countryKey}::${regionKey}::${county.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}::${county.id}`;
                          return (
                            <div
                              key={countyKey}
                              className={`flex items-center p-3 pl-20 hover:bg-blue-50 cursor-pointer transition-colors border-t border-gray-100 first:border-t-0 ${
                                disabled ? 'cursor-not-allowed opacity-60' : ''
                              }`}
                              onClick={() => !disabled && toggleSelected(countyKey)}
                            >
                              <div className="flex items-center space-x-3 w-full">
                                <div className="relative">
                                  <input
                                    type={allowMultiple ? "checkbox" : "radio"}
                                    checked={isSelected(countyKey)}
                                    onChange={() => {}}
                                    disabled={disabled}
                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 disabled:cursor-not-allowed"
                                  />
                                </div>
                                <span className={`text-sm transition-colors ${
                                  isSelected(countyKey)
                                    ? 'text-blue-700 font-medium'
                                    : 'text-gray-700'
                                }`}>
                                  {county.name}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* No Results Message */}
      {searchTerm && Object.keys(data).length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>No counties found matching "{searchTerm}"</p>
          <button
            onClick={clearSearch}
            disabled={disabled}
            className="mt-2 text-blue-600 hover:text-blue-800 underline disabled:cursor-not-allowed"
          >
            Clear search
          </button>
        </div>
      )}
    </div>
  );
};

export default LocationAccordion;

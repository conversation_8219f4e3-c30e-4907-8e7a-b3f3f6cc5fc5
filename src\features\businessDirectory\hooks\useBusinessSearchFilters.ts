import { useState, useMemo } from 'react';
import type { SortOption } from '../components/BusinessSearchAndFilter';
import type { Database } from '@/types/database.types';

type Business = Database['public']['Tables']['businesses']['Row'];

export interface UseBusinessSearchFilters {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  locationFilter: string;
  setLocationFilter: (filter: string) => void;
  sortBy: SortOption;
  setSortBy: (sort: SortOption) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
  locationOptions: {
    cities: string[];
    postcodes: string[];
  };
  filteredAndSortedBusinesses: Business[];
}

export const useBusinessSearchFilters = (businesses: Business[]): UseBusinessSearchFilters => {
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('all');
  const [sortBy, setSortBy] = useState<SortOption>('name_asc');
  const [showFilters, setShowFilters] = useState(false);

  // Get unique cities and postcodes for filter options
  const locationOptions = useMemo(() => {
    const cities = new Set<string>();
    const postcodes = new Set<string>();
    
    businesses.forEach(business => {
      if (business.city) cities.add(business.city);
      if (business.postcode) postcodes.add(business.postcode);
    });
    
    return {
      cities: Array.from(cities).sort(),
      postcodes: Array.from(postcodes).sort()
    };
  }, [businesses]);

  // Filter and sort businesses
  const filteredAndSortedBusinesses = useMemo(() => {
    let filtered = businesses;

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(business => 
        business.business_name.toLowerCase().includes(term) ||
        business.contact_email.toLowerCase().includes(term) ||
        business.city?.toLowerCase().includes(term) ||
        business.postcode?.toLowerCase().includes(term) ||
        business.website?.toLowerCase().includes(term)
      );
    }

    // Apply location filter
    if (locationFilter && locationFilter !== 'all') {
      filtered = filtered.filter(business => 
        business.city === locationFilter || business.postcode === locationFilter
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name_asc':
          return a.business_name.localeCompare(b.business_name);
        case 'name_desc':
          return b.business_name.localeCompare(a.business_name);
        case 'date_asc':
          return new Date(a.created_at || '').getTime() - new Date(b.created_at || '').getTime();
        case 'date_desc':
          return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [businesses, searchTerm, locationFilter, sortBy]);

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setLocationFilter('all');
    setSortBy('name_asc');
  };

  // Check if any filters are active
  const hasActiveFilters = !!(searchTerm || (locationFilter && locationFilter !== 'all') || sortBy !== 'name_asc');

  return {
    searchTerm,
    setSearchTerm,
    locationFilter,
    setLocationFilter,
    sortBy,
    setSortBy,
    showFilters,
    setShowFilters,
    clearFilters,
    hasActiveFilters,
    locationOptions,
    filteredAndSortedBusinesses,
  };
};

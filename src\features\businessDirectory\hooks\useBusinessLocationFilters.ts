import { useState, useMemo, useEffect } from 'react';
import { supabase } from '../../../integrations/supabase/client';
import type { SortOption } from '../components/BusinessSearchWithLocation';
import type { Database } from '../../../types/database.types';

// For now, use the basic Business type until location tables are properly typed
type Business = Database['public']['Tables']['businesses']['Row'];

export interface UseBusinessLocationFilters {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  sortBy: SortOption;
  setSortBy: (sort: SortOption) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  selectedHQLocations: Set<string>;
  setSelectedHQLocations: (locations: Set<string>) => void;
  selectedCustomerLocations: Set<string>;
  setSelectedCustomerLocations: (locations: Set<string>) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
  filteredAndSortedBusinesses: Business[];
  totalBusinessCount: number;
  loading: boolean;
  error: string | null;
  applyLocationFilters: () => Promise<void>;
}

export const useBusinessLocationFilters = (): UseBusinessLocationFilters => {
  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('name_asc');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedHQLocations, setSelectedHQLocations] = useState<Set<string>>(new Set());
  const [selectedCustomerLocations, setSelectedCustomerLocations] = useState<Set<string>>(new Set());
  
  // Data state
  const [allBusinesses, setAllBusinesses] = useState<Business[]>([]);
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load all businesses initially
  useEffect(() => {
    const loadAllBusinesses = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .order('business_name');

        if (error) throw error;
        
        const businessList = (data || []) as Business[];
        setAllBusinesses(businessList);
        setBusinesses(businessList); // Initially show all businesses
        
        console.log('Loaded businesses:', businessList.length);
      } catch (err) {
        console.error('Failed to load businesses:', err);
        setError(err instanceof Error ? err.message : 'Failed to load businesses');
      } finally {
        setLoading(false);
      }
    };

    loadAllBusinesses();
  }, []);

  // Apply location-based filters (simplified client-side filtering for now)
  const applyLocationFilters = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Applying location filters:', {
        hqLocations: Array.from(selectedHQLocations),
        customerLocations: Array.from(selectedCustomerLocations)
      });

      // If no location filters are selected, show all businesses
      if (selectedHQLocations.size === 0 && selectedCustomerLocations.size === 0) {
        console.log('No location filters, showing all businesses');
        setBusinesses(allBusinesses);
        setLoading(false);
        return;
      }

      // For now, implement basic client-side filtering based on city/postcode
      // This is a temporary solution until the location tables are properly set up
      let filtered = allBusinesses;

      if (selectedHQLocations.size > 0) {
        // Extract location names from the selection keys for basic matching
        const locationNames = Array.from(selectedHQLocations).map(key => {
          const parts = key.split('-');
          if (parts.length >= 3) {
            // Convert the last part back to a readable name
            return parts.slice(2).join(' ')
              .split('-')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ');
          }
          return '';
        }).filter(Boolean);

        console.log('Filtering by location names:', locationNames);

        filtered = filtered.filter(business => {
          return locationNames.some(locationName => 
            business.city?.toLowerCase().includes(locationName.toLowerCase()) ||
            business.postcode?.toLowerCase().includes(locationName.toLowerCase())
          );
        });
      }

      console.log('Filtered businesses result:', filtered);
      setBusinesses(filtered);
    } catch (err) {
      console.error('Failed to apply location filters:', err);
      setError(err instanceof Error ? err.message : 'Failed to filter businesses');
    } finally {
      setLoading(false);
    }
  };

  // Apply search and sort filters to the location-filtered businesses
  const filteredAndSortedBusinesses = useMemo(() => {
    let filtered = businesses;

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(business => 
        business.business_name.toLowerCase().includes(term) ||
        business.contact_email.toLowerCase().includes(term) ||
        business.city?.toLowerCase().includes(term) ||
        business.postcode?.toLowerCase().includes(term) ||
        business.website?.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name_asc':
          return a.business_name.localeCompare(b.business_name);
        case 'name_desc':
          return b.business_name.localeCompare(a.business_name);
        case 'date_asc':
          return new Date(a.created_at || '').getTime() - new Date(b.created_at || '').getTime();
        case 'date_desc':
          return new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [businesses, searchTerm, sortBy]);

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedHQLocations(new Set());
    setSelectedCustomerLocations(new Set());
    setSortBy('name_asc');
    setBusinesses(allBusinesses); // Restore all businesses when filters are cleared
  };

  // Check if any filters are active
  const hasActiveFilters = !!(
    searchTerm || 
    selectedHQLocations.size > 0 || 
    selectedCustomerLocations.size > 0 || 
    sortBy !== 'name_asc'
  );

  // Auto-apply location filters when location selections change
  useEffect(() => {
    // Don't apply filters during initial load
    if (allBusinesses.length === 0) return;
    
    console.log('useEffect triggered - applying location filters');
    applyLocationFilters();
  }, [selectedHQLocations, selectedCustomerLocations]);

  return {
    searchTerm,
    setSearchTerm,
    sortBy,
    setSortBy,
    showFilters,
    setShowFilters,
    selectedHQLocations,
    setSelectedHQLocations,
    selectedCustomerLocations,
    setSelectedCustomerLocations,
    clearFilters,
    hasActiveFilters,
    filteredAndSortedBusinesses,
    totalBusinessCount: allBusinesses.length,
    loading,
    error,
    applyLocationFilters,
  };
};

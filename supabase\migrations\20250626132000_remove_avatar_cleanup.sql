-- Remove avatar cleanup queue table as image deletion is now handled directly in application
-- This cleanup table is no longer needed as we handle deletions synchronously

-- Remove the avatar cleanup queue table if it exists
DROP TABLE IF EXISTS public.avatar_cleanup_queue;

-- Remove the cleanup trigger and function if they exist
DROP TRIGGER IF EXISTS profile_deletion_cleanup ON public.profiles;
DROP FUNCTION IF EXISTS public.cleanup_profile_assets();

-- Migration completed: Removed avatar cleanup infrastructure - deletions now handled directly in application

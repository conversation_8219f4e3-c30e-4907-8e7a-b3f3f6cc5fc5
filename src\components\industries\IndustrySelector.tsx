import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Building2 } from 'lucide-react';
import { UKIndustryService } from '@/services/ukIndustryService';
import type { 
  UKIndustryWithChildren, 
  IndustrySelectionState 
} from '@/types/uk-industries.types';
import { Label } from '@/components/ui/label';

interface IndustrySelectorProps {
  selectedIndustryId?: string | null;
  selectedTargetIndustryIds?: string[];
  onIndustryChange?: (industryId: string | null) => void;
  onTargetIndustriesChange?: (industryIds: string[]) => void;
  mode: 'single' | 'multi' | 'both';
  title?: string;
  description?: string;
  singleSelectLabel?: string;
  multiSelectLabel?: string;
  maxSelections?: number;
  allowParentSelection?: boolean;
  className?: string;
}

export const IndustrySelector: React.FC<IndustrySelectorProps> = ({
  selectedIndustryId,
  selectedTargetIndustryIds = [],
  onIndustryChange,
  onTargetIndustriesChange,
  mode,
  title = "Industry Selection",
  description = "Select the relevant industries",
  singleSelectLabel = "Your Industry",
  multiSelectLabel = "Target Industries",
  maxSelections,
  allowParentSelection = false,
  className = ""
}) => {
  const [industries, setIndustries] = useState<UKIndustryWithChildren[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectionState, setSelectionState] = useState<IndustrySelectionState>({
    selectedIndustry: selectedIndustryId || null,
    selectedTargetIndustries: new Set(selectedTargetIndustryIds),
    expandedCategories: new Set()
  });

  useEffect(() => {
    loadIndustries();
  }, []);

  useEffect(() => {
    setSelectionState(prev => ({
      ...prev,
      selectedIndustry: selectedIndustryId || null,
      selectedTargetIndustries: new Set(selectedTargetIndustryIds)
    }));
  }, [selectedIndustryId, selectedTargetIndustryIds]);

  const loadIndustries = async () => {
    try {
      setLoading(true);
      const data = await UKIndustryService.getAllIndustriesWithChildren();
      setIndustries(data);
      
      // Auto-expand categories that have selected items
      const expandedCategories = new Set<string>();
      data.forEach(parent => {
        const hasSelectedChild = parent.children.some(child => 
          selectedIndustryId === child.id || selectedTargetIndustryIds.includes(child.id)
        );
        const hasSelectedParent = selectedIndustryId === parent.id || selectedTargetIndustryIds.includes(parent.id);
        
        if (hasSelectedChild || hasSelectedParent) {
          expandedCategories.add(parent.id);
        }
      });
      
      setSelectionState(prev => ({
        ...prev,
        expandedCategories
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load industries');
    } finally {
      setLoading(false);
    }
  };

  const handleSingleSelect = (industryId: string) => {
    const newSelection = selectionState.selectedIndustry === industryId ? null : industryId;
    setSelectionState(prev => ({
      ...prev,
      selectedIndustry: newSelection
    }));
    onIndustryChange?.(newSelection);
  };

  const handleMultiSelect = (industryId: string) => {
    const newSelected = new Set(selectionState.selectedTargetIndustries);
    
    if (newSelected.has(industryId)) {
      newSelected.delete(industryId);
    } else {
      // Check max selections limit
      if (maxSelections && newSelected.size >= maxSelections) {
        return; // Don't allow more selections
      }
      newSelected.add(industryId);
    }
    
    const newSelectedArray = Array.from(newSelected);
    setSelectionState(prev => ({
      ...prev,
      selectedTargetIndustries: newSelected
    }));
    onTargetIndustriesChange?.(newSelectedArray);
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setSelectionState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedCategories: newExpanded
      };
    });
  };

  const getSelectedCount = () => selectionState.selectedTargetIndustries.size;

  const renderIndustryOption = (industry: UKIndustryWithChildren, isChild = false) => {
    const canSelect = allowParentSelection || isChild;
    const isSelectedSingle = selectionState.selectedIndustry === industry.id;
    const isSelectedMulti = selectionState.selectedTargetIndustries.has(industry.id);
    const isMaxReached = maxSelections && getSelectedCount() >= maxSelections && !isSelectedMulti;

    return (
      <div key={industry.id} className={`${isChild ? 'ml-6' : ''} space-y-2`}>
        <div className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-muted/30">
          {mode === 'single' && canSelect && (
            <RadioGroupItem
              value={industry.id}
              id={`single-${industry.id}`}
              checked={isSelectedSingle}
              onClick={() => handleSingleSelect(industry.id)}
              disabled={!canSelect}
            />
          )}
          
          {(mode === 'multi' || mode === 'both') && canSelect && (
            <Checkbox
              id={`multi-${industry.id}`}
              checked={isSelectedMulti}
              onCheckedChange={() => handleMultiSelect(industry.id)}
              disabled={!canSelect || isMaxReached}
            />
          )}
          
          <div className="flex-1 min-w-0">
            <label
              htmlFor={mode === 'single' ? `single-${industry.id}` : `multi-${industry.id}`}
              className={`text-sm font-medium cursor-pointer ${!canSelect ? 'text-muted-foreground' : ''}`}
            >
              {industry.name}
              {!isChild && (
                <Badge variant="outline" className="ml-2 text-xs">
                  {industry.children.length} subcategories
                </Badge>
              )}
            </label>
            {industry.description && (
              <p className="text-xs text-muted-foreground mt-1">
                {industry.description}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-blue-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading industries...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-blue-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error: {error}</p>
            <Button onClick={loadIndustries} variant="outline" className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-blue-600" />
          {title}
        </CardTitle>
        <CardDescription>
          {description}
          {maxSelections && mode !== 'single' && (
            <span className="block mt-1 text-sm">
              Selected: {getSelectedCount()}/{maxSelections}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {mode === 'single' && (
          <div>
            <Label className="text-sm font-medium">{singleSelectLabel}</Label>
            <div className="mt-2 space-y-4">
              {industries.map((parent) => (
                <Collapsible
                  key={parent.id}
                  open={selectionState.expandedCategories.has(parent.id)}
                  onOpenChange={() => toggleCategoryExpansion(parent.id)}
                >
                  <div className="space-y-2">
                    {renderIndustryOption(parent)}
                    
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-3 h-auto p-1 text-xs"
                      >
                        {selectionState.expandedCategories.has(parent.id) ? (
                          <ChevronDown className="w-3 h-3 mr-1" />
                        ) : (
                          <ChevronRight className="w-3 h-3 mr-1" />
                        )}
                        {selectionState.expandedCategories.has(parent.id) ? 'Hide' : 'Show'} subcategories
                      </Button>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="space-y-1">
                      {parent.children.map((child) => renderIndustryOption(child, true))}
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              ))}
            </div>
          </div>
        )}

        {(mode === 'multi' || mode === 'both') && (
          <div>
            <Label className="text-sm font-medium">{multiSelectLabel}</Label>
            <div className="mt-2 space-y-4">
              {industries.map((parent) => (
                <Collapsible
                  key={parent.id}
                  open={selectionState.expandedCategories.has(parent.id)}
                  onOpenChange={() => toggleCategoryExpansion(parent.id)}
                >
                  <div className="space-y-2">
                    {renderIndustryOption(parent)}
                    
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-3 h-auto p-1 text-xs"
                      >
                        {selectionState.expandedCategories.has(parent.id) ? (
                          <ChevronDown className="w-3 h-3 mr-1" />
                        ) : (
                          <ChevronRight className="w-3 h-3 mr-1" />
                        )}
                        {selectionState.expandedCategories.has(parent.id) ? 'Hide' : 'Show'} subcategories
                      </Button>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent className="space-y-1">
                      {parent.children.map((child) => renderIndustryOption(child, true))}
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

import { useState, useEffect } from 'react';
import { LocationService } from '../services/locationService';
import type { LocationHierarchy, BusinessWithLocations, LocationSearchResult } from '../types/location.types';

export const useLocations = () => {
  const [locationHierarchy, setLocationHierarchy] = useState<LocationHierarchy>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadLocationHierarchy = async () => {
    try {
      setLoading(true);
      setError(null);
      const hierarchy = await LocationService.getLocationHierarchy();
      setLocationHierarchy(hierarchy);
    } catch (err) {
      console.error('Failed to load location hierarchy:', err);
      setError('Failed to load locations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLocationHierarchy();
  }, []);

  return {
    locationHierarchy,
    loading,
    error,
    reload: loadLocationHierarchy
  };
};

export const useLocationSearch = () => {
  const [results, setResults] = useState<LocationSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const searchResults = await LocationService.searchLocations(query);
      setResults(searchResults);
    } catch (err) {
      console.error('Failed to search locations:', err);
      setError('Failed to search locations');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
    setError(null);
  };

  return {
    results,
    loading,
    error,
    search,
    clearResults
  };
};

export const useBusinessLocations = (businessId?: string) => {
  const [business, setBusiness] = useState<BusinessWithLocations | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  const loadBusiness = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const businessData = await LocationService.getBusinessWithLocations(id);
      setBusiness(businessData);
    } catch (err) {
      console.error('Failed to load business locations:', err);
      setError('Failed to load business locations');
    } finally {
      setLoading(false);
    }
  };

  const updateHeadquarters = async (locationId: string | null) => {
    if (!business) return;

    try {
      setUpdating(true);
      await LocationService.updateBusinessHeadquarters(business.id, locationId);
      await loadBusiness(business.id); // Reload to get updated data
    } catch (err) {
      console.error('Failed to update headquarters:', err);
      throw err;
    } finally {
      setUpdating(false);
    }
  };

  const updateCustomerLocations = async (selectedKeys: string[]) => {
    if (!business) return;

    try {
      setUpdating(true);
      const locationIds = await LocationService.convertSelectionKeysToIds(selectedKeys);
      await LocationService.setBusinessCustomerLocations(business.id, locationIds);
      await loadBusiness(business.id); // Reload to get updated data
    } catch (err) {
      console.error('Failed to update customer locations:', err);
      throw err;
    } finally {
      setUpdating(false);
    }
  };

  const updateAllLocations = async (
    headquartersLocationId: string | null,
    customerLocationKeys: string[]
  ) => {
    if (!business) return;

    try {
      setUpdating(true);
      const customerLocationIds = await LocationService.convertSelectionKeysToIds(customerLocationKeys);
      
      await LocationService.updateBusinessLocations({
        business_id: business.id,
        headquarters_location_id: headquartersLocationId,
        customer_location_ids: customerLocationIds
      });
      
      await loadBusiness(business.id); // Reload to get updated data
    } catch (err) {
      console.error('Failed to update business locations:', err);
      throw err;
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    if (businessId) {
      loadBusiness(businessId);
    }
  }, [businessId]);

  return {
    business,
    loading,
    error,
    updating,
    updateHeadquarters,
    updateCustomerLocations,
    updateAllLocations,
    reload: businessId ? () => loadBusiness(businessId) : undefined
  };
};

export const useBusinessLocationFilter = () => {
  const [businesses, setBusinesses] = useState<BusinessWithLocations[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const filterBusinesses = async (
    headquartersLocationIds?: string[],
    customerLocationIds?: string[]
  ) => {
    try {
      setLoading(true);
      setError(null);
      const results = await LocationService.getBusinessesByLocation(
        headquartersLocationIds,
        customerLocationIds
      );
      setBusinesses(results);
    } catch (err) {
      console.error('Failed to filter businesses:', err);
      setError('Failed to filter businesses');
      setBusinesses([]);
    } finally {
      setLoading(false);
    }
  };

  const clearFilter = () => {
    setBusinesses([]);
    setError(null);
  };

  return {
    businesses,
    loading,
    error,
    filterBusinesses,
    clearFilter
  };
};

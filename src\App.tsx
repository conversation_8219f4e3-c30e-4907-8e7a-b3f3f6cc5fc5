
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import Layout from "@/components/Layout";

// Feature imports
import { HomePage } from "@/features/home";
import { AuthPage } from "@/features/auth";
import { ProfilePage } from "@/features/profile";
import { MembersPage, MemberDetailPage } from "@/features/members";
import { BusinessDirectoryPage, BusinessDetailPage, BusinessManagementPage } from "@/features/businessDirectory";
import { EventsPage } from "@/features/events";
import { FundingFinderPage } from "@/features/fundingFinder";
import { JobsPage } from "@/features/jobs";

// Shared pages
import NotFoundPage from "./pages/NotFoundPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Layout>
            <Routes>
              {/* Core Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/members" element={<MembersPage />} />
              <Route path="/members/:memberId" element={<MemberDetailPage />} />
              
              {/* Feature Routes */}
              <Route path="/business-directory" element={<BusinessDirectoryPage />} />
              <Route path="/business/:businessId" element={<BusinessDetailPage />} />
              <Route path="/business-management" element={<BusinessManagementPage />} />
              <Route path="/events" element={<EventsPage />} />
              <Route path="/funding" element={<FundingFinderPage />} />
              <Route path="/jobs" element={<JobsPage />} />
              
              {/* Catch-all Route */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;

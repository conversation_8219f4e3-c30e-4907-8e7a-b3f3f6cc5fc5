-- Create Net-Zero Categories System
-- This migration creates tables for net-zero categories and subcategories,
-- and junction tables for user and business category selections

-- Create the main categories table
CREATE TABLE IF NOT EXISTS public.netzero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create the subcategories table
CREATE TABLE IF NOT EXISTS public.netzero_subcategories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id UUID NOT NULL REFERENCES public.netzero_categories(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(category_id, name)
);

-- Create junction table for user profile interests (many-to-many)
CREATE TABLE IF NOT EXISTS public.profile_netzero_interests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    subcategory_id UUID NOT NULL REFERENCES public.netzero_subcategories(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(profile_id, subcategory_id)
);

-- Create junction table for business categories (many-to-many, but typically one primary)
CREATE TABLE IF NOT EXISTS public.business_netzero_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    subcategory_id UUID NOT NULL REFERENCES public.netzero_subcategories(id) ON DELETE CASCADE,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, subcategory_id)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_netzero_subcategories_category_id ON public.netzero_subcategories(category_id);
CREATE INDEX IF NOT EXISTS idx_profile_netzero_interests_profile_id ON public.profile_netzero_interests(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_netzero_interests_subcategory_id ON public.profile_netzero_interests(subcategory_id);
CREATE INDEX IF NOT EXISTS idx_business_netzero_categories_business_id ON public.business_netzero_categories(business_id);
CREATE INDEX IF NOT EXISTS idx_business_netzero_categories_subcategory_id ON public.business_netzero_categories(subcategory_id);
CREATE INDEX IF NOT EXISTS idx_business_netzero_categories_primary ON public.business_netzero_categories(business_id, is_primary);

-- Add updated_at triggers
CREATE OR REPLACE TRIGGER handle_netzero_categories_updated_at 
    BEFORE UPDATE ON public.netzero_categories 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER handle_netzero_subcategories_updated_at 
    BEFORE UPDATE ON public.netzero_subcategories 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Grant permissions
GRANT ALL ON TABLE public.netzero_categories TO anon;
GRANT ALL ON TABLE public.netzero_categories TO authenticated;
GRANT ALL ON TABLE public.netzero_categories TO service_role;

GRANT ALL ON TABLE public.netzero_subcategories TO anon;
GRANT ALL ON TABLE public.netzero_subcategories TO authenticated;
GRANT ALL ON TABLE public.netzero_subcategories TO service_role;

GRANT ALL ON TABLE public.profile_netzero_interests TO anon;
GRANT ALL ON TABLE public.profile_netzero_interests TO authenticated;
GRANT ALL ON TABLE public.profile_netzero_interests TO service_role;

GRANT ALL ON TABLE public.business_netzero_categories TO anon;
GRANT ALL ON TABLE public.business_netzero_categories TO authenticated;
GRANT ALL ON TABLE public.business_netzero_categories TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.netzero_categories IS 'Main net-zero categories (Energy, Transportation, etc.)';
COMMENT ON TABLE public.netzero_subcategories IS 'Subcategories within each main net-zero category';
COMMENT ON TABLE public.profile_netzero_interests IS 'User interests in net-zero subcategories (many-to-many)';
COMMENT ON TABLE public.business_netzero_categories IS 'Business categories in net-zero subcategories (many-to-many)';
COMMENT ON COLUMN public.business_netzero_categories.is_primary IS 'Indicates if this is the primary category for the business';

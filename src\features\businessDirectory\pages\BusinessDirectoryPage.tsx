import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MapPin, Globe, Calendar, Building2, Search, X } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import BusinessLogoDisplay from '../components/BusinessLogoDisplay';
import type { Database } from '@/types/database.types';

type Business = Database['public']['Tables']['businesses']['Row'];

// Extended business type with location info
interface BusinessWithLocation extends Business {
  headquarters_location?: {
    id: string;
    name: string;
    slug: string;
    path: string;
  } | null;
  customer_locations?: Array<{
    id: string;
    name: string;
    slug: string;
    type: string;
    path: string;
  }> | null;
}

const BusinessDirectoryPage: React.FC = () => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<any[]>([]); // Using any for now to bypass TypeScript issues
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchBusinessesWithLocations = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all businesses with their full details - using any to bypass TypeScript issues
        const { data: businessData, error: businessError } = await supabase
          .from('businesses' as any)
          .select('*')
          .order('business_name');

        if (businessError) throw businessError;

        console.log('Business data sample:', businessData?.[0]); // Debug log

        // For each business, try to get headquarters location details manually
        const businessesWithLocations = await Promise.all(
          (businessData || []).map(async (business: any) => {
            let headquartersLocation = null;
            
            if (business.headquarters_location_id) {
              try {
                // Get headquarters location details - using any to bypass TypeScript issues
                const { data: hqLocationData, error: hqLocationError } = await supabase
                  .from('locations' as any)
                  .select('id, name, slug, type')
                  .eq('id', business.headquarters_location_id)
                  .single();

                if (!hqLocationError && hqLocationData) {
                  console.log('HQ Location data for', business.business_name, ':', hqLocationData); // Debug log
                  const locationInfo = hqLocationData as any; // Cast to any to bypass TypeScript issues
                  headquartersLocation = {
                    headquarters_name: locationInfo.name,
                    headquarters_slug: locationInfo.slug,
                    headquarters_id: locationInfo.id,
                    headquarters_path: null // We'll calculate this later if needed
                  };
                }
              } catch (err) {
                console.warn('Could not fetch HQ location for business:', business.business_name, err);
              }
            }

            return {
              ...business,
              headquarters_location: headquartersLocation
            };
          })
        );

        console.log('Businesses with locations sample:', businessesWithLocations?.[0]); // Debug log
        setBusinesses(businessesWithLocations);
      } catch (err) {
        console.error('Failed to fetch businesses:', err);
        setError(err instanceof Error ? err.message : 'Failed to load businesses');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessesWithLocations();
  }, []);

  // Filter businesses based on search term
  const filteredBusinesses = businesses.filter(business => 
    !searchTerm || 
    business.business_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    business.contact_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    business.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    business.postcode?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Business Directory</h1>
        <p className="text-muted-foreground">
          Browse sustainable businesses in our network with their headquarters locations.
        </p>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search businesses or locations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setSearchTerm('')}
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>
      
      {loading && (
        <div className="text-center py-8">
          <p>Loading businesses...</p>
        </div>
      )}

      {error && (
        <div className="p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      {!loading && !error && filteredBusinesses.length === 0 && (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg">
            {searchTerm ? 'No businesses match your search' : 'No businesses found'}
          </p>
          <p className="text-muted-foreground mt-2">
            {searchTerm ? 'Try adjusting your search terms.' : 'Be the first to add your sustainable business to our directory.'}
          </p>
        </div>
      )}

      {!loading && !error && filteredBusinesses.length > 0 && (
        <>
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              {searchTerm 
                ? `Showing ${filteredBusinesses.length} of ${businesses.length} businesses`
                : `${businesses.length} businesses`
              }
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredBusinesses.map((business) => (
              <Card 
                key={business.id} 
                className="cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => navigate(`/business/${business.id}`)}
              >
                <CardHeader className="pb-2">
                  {/* Business name at the top */}
                  <CardTitle className="text-lg leading-tight mb-2">
                    {business.business_name}
                  </CardTitle>
                  
                  {/* Visual divider after business name */}
                  <div className="w-full h-px bg-border mb-4"></div>
                  
                  {/* Content area with image on the right */}
                  <div className="flex items-start gap-4">
                    <div className="flex-1 min-w-0 space-y-2.5">
                      {/* Headquarters Location - always show with icon for consistent alignment */}
                      <div className="flex items-start gap-2">
                        <Building2 className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <div className="min-w-0">
                          <div className="text-sm font-medium">Headquarters</div>
                          {(business as any).headquarters_location?.headquarters_name ? (
                            <div className="text-sm font-medium text-green-600 truncate">
                              {(business as any).headquarters_location.headquarters_name}
                            </div>
                          ) : business.headquarters_location_id ? (
                            <div className="text-xs text-muted-foreground font-mono truncate">
                              ID: {business.headquarters_location_id}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground italic">
                              Not specified
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Website - always show with icon for consistent alignment */}
                      <div className="flex items-center gap-2 text-sm">
                        <Globe className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          {business.website ? (
                            <a 
                              href={business.website} 
                              target="_blank" 
                              rel="noopener noreferrer" 
                              className="text-primary hover:underline truncate block"
                              onClick={(e) => e.stopPropagation()}
                            >
                              {business.website.replace(/^https?:\/\//, '')}
                            </a>
                          ) : (
                            <span className="text-muted-foreground italic">No website</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {/* Larger business image on the right (1.25x bigger: 100px) */}
                    <div className="flex-shrink-0">
                      <BusinessLogoDisplay
                        logoUrl={business.logo_url}
                        businessName={business.business_name}
                        size="xl"
                        shape="square"
                        className="w-[100px] h-[100px]"
                      />
                    </div>
                  </div>
                </CardHeader>
                
                {/* Visual divider before date */}
                <div className="px-6">
                  <div className="w-full h-px bg-border"></div>
                </div>
                
                <CardContent className="pt-2 pb-4">
                  <div className="flex items-center gap-1.5 text-muted-foreground text-xs">
                    <Calendar className="w-3 h-3" />
                    Added {new Date(business.created_at || '').toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default BusinessDirectoryPage;

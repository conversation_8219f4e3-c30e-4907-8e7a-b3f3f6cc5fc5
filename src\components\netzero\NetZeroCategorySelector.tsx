import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronRight, Leaf } from 'lucide-react';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import type { 
  NetZeroCategoryWithSubcategories, 
  CategorySelectionState 
} from '@/types/netzero-categories.types';

interface NetZeroCategorySelectorProps {
  selectedSubcategories: string[];
  onSelectionChange: (subcategoryIds: string[]) => void;
  primarySubcategoryId?: string;
  onPrimaryChange?: (subcategoryId: string | undefined) => void;
  allowPrimarySelection?: boolean;
  maxSelections?: number;
  title?: string;
  description?: string;
  className?: string;
}

export const NetZeroCategorySelector: React.FC<NetZeroCategorySelectorProps> = ({
  selectedSubcategories,
  onSelectionChange,
  primarySubcategoryId,
  onPrimaryChange,
  allowPrimarySelection = false,
  maxSelections,
  title = "Net-Zero Categories",
  description = "Select the categories that best represent your interests or business focus",
  className = ""
}) => {
  const [categories, setCategories] = useState<NetZeroCategoryWithSubcategories[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectionState, setSelectionState] = useState<CategorySelectionState>({
    selectedSubcategories: new Set(selectedSubcategories),
    expandedCategories: new Set()
  });

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    setSelectionState(prev => ({
      ...prev,
      selectedSubcategories: new Set(selectedSubcategories)
    }));
  }, [selectedSubcategories]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await NetZeroCategoryService.getAllCategoriesWithSubcategories();
      setCategories(data);
      
      // Auto-expand categories that have selected subcategories
      const expandedCategories = new Set<string>();
      data.forEach(category => {
        if (category.subcategories.some(sub => selectedSubcategories.includes(sub.id))) {
          expandedCategories.add(category.id);
        }
      });
      
      setSelectionState(prev => ({
        ...prev,
        expandedCategories
      }));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const handleSubcategoryToggle = (subcategoryId: string) => {
    const newSelected = new Set(selectionState.selectedSubcategories);
    
    if (newSelected.has(subcategoryId)) {
      newSelected.delete(subcategoryId);
      // If this was the primary, clear primary selection
      if (primarySubcategoryId === subcategoryId && onPrimaryChange) {
        onPrimaryChange(undefined);
      }
    } else {
      // Check max selections limit
      if (maxSelections && newSelected.size >= maxSelections) {
        return; // Don't allow more selections
      }
      newSelected.add(subcategoryId);
    }
    
    const newSelectedArray = Array.from(newSelected);
    setSelectionState(prev => ({
      ...prev,
      selectedSubcategories: newSelected
    }));
    onSelectionChange(newSelectedArray);
  };

  const handlePrimaryToggle = (subcategoryId: string) => {
    if (!onPrimaryChange) return;
    
    if (primarySubcategoryId === subcategoryId) {
      onPrimaryChange(undefined);
    } else {
      // Ensure the subcategory is selected first
      if (!selectionState.selectedSubcategories.has(subcategoryId)) {
        handleSubcategoryToggle(subcategoryId);
      }
      onPrimaryChange(subcategoryId);
    }
  };

  const toggleCategoryExpansion = (categoryId: string) => {
    setSelectionState(prev => {
      const newExpanded = new Set(prev.expandedCategories);
      if (newExpanded.has(categoryId)) {
        newExpanded.delete(categoryId);
      } else {
        newExpanded.add(categoryId);
      }
      return {
        ...prev,
        expandedCategories: newExpanded
      };
    });
  };

  const getSelectedCount = () => selectionState.selectedSubcategories.size;

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Leaf className="w-5 h-5 text-green-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading categories...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Leaf className="w-5 h-5 text-green-600" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600">Error: {error}</p>
            <Button onClick={loadCategories} variant="outline" className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Leaf className="w-5 h-5 text-green-600" />
          {title}
        </CardTitle>
        <CardDescription>
          {description}
          {maxSelections && (
            <span className="block mt-1 text-sm">
              Selected: {getSelectedCount()}/{maxSelections}
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {categories.map((category) => {
          const isExpanded = selectionState.expandedCategories.has(category.id);
          const selectedInCategory = category.subcategories.filter(sub => 
            selectionState.selectedSubcategories.has(sub.id)
          ).length;
          
          return (
            <Collapsible
              key={category.id}
              open={isExpanded}
              onOpenChange={() => toggleCategoryExpansion(category.id)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-between p-4 h-auto border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                    <div className="text-left">
                      <h3 className="font-medium">{category.name}</h3>
                      {category.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {category.description}
                        </p>
                      )}
                    </div>
                  </div>
                  {selectedInCategory > 0 && (
                    <Badge variant="secondary">
                      {selectedInCategory} selected
                    </Badge>
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-2 ml-4 space-y-2">
                {category.subcategories.map((subcategory) => {
                  const isSelected = selectionState.selectedSubcategories.has(subcategory.id);
                  const isPrimary = primarySubcategoryId === subcategory.id;
                  const isMaxReached = maxSelections && getSelectedCount() >= maxSelections && !isSelected;
                  
                  return (
                    <div
                      key={subcategory.id}
                      className="flex items-start gap-3 p-3 rounded-lg border bg-card hover:bg-muted/30"
                    >
                      <Checkbox
                        id={subcategory.id}
                        checked={isSelected}
                        onCheckedChange={() => handleSubcategoryToggle(subcategory.id)}
                        disabled={isMaxReached}
                      />
                      <div className="flex-1 min-w-0">
                        <label
                          htmlFor={subcategory.id}
                          className="text-sm font-medium cursor-pointer"
                        >
                          {subcategory.name}
                        </label>
                        {subcategory.description && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {subcategory.description}
                          </p>
                        )}
                      </div>
                      {allowPrimarySelection && isSelected && (
                        <Button
                          size="sm"
                          variant={isPrimary ? "default" : "outline"}
                          onClick={() => handlePrimaryToggle(subcategory.id)}
                          className="text-xs"
                        >
                          {isPrimary ? "Primary" : "Set Primary"}
                        </Button>
                      )}
                    </div>
                  );
                })}
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </CardContent>
    </Card>
  );
};

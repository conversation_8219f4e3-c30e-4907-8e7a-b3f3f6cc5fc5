import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import AvatarDisplay from './AvatarDisplay';

// Profile sidebar component with improved layout and rounded square avatar

interface SidebarProps {
  firstName: string | null | undefined;
  lastName?: string | null;
  email?: string | null;
  jobTitle?: string | null;
  avatarUrl?: string | null;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  showBusinessMenu?: boolean;
}

const Sidebar = ({ firstName, lastName, email, jobTitle, avatarUrl, activeTab = 'dashboard', onTabChange, showBusinessMenu = true }: SidebarProps) => {
  // Create full name or fallback to email username
  const getDisplayName = () => {
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }
    if (firstName) {
      return firstName;
    }
    if (lastName) {
      return lastName;
    }
    return email?.split('@')[0] || 'User';
  };
  
  const displayName = getDisplayName();
  
  const handleTabClick = (tab: string) => {
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <Card className="lg:col-span-1">
      <CardHeader className="pb-4">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="flex-shrink-0">
            <AvatarDisplay
              avatarUrl={avatarUrl}
              firstName={firstName}
              lastName={lastName}
              email={email}
              size="xl"
              shape="square"
            />
          </div>
          <div className="min-w-0 w-full max-w-full px-1">
            <CardTitle className="text-lg leading-tight break-words word-wrap truncate-multiline text-center">
              {displayName}
            </CardTitle>
            <CardDescription className="break-words text-sm mt-1 leading-relaxed text-center">
              {jobTitle || 'Member'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <nav className="space-y-2">
          <div 
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'dashboard' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('dashboard')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect width="18" height="18" x="3" y="3" rx="2" />
              <path d="M3 9h18" />
              <path d="M9 21V9" />
            </svg>
            <span>Dashboard</span>
          </div>
          
          <div 
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'profile' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('profile')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <span>Profile</span>
          </div>
          
          <div 
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'notifications' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('notifications')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
              <path d="M13.73 21a2 2 0 0 1-3.46 0" />
            </svg>
            <span>Notifications</span>
          </div>
          
          <div 
            className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'account' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
            onClick={() => handleTabClick('account')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19.69 14a6.9 6.9 0 0 0 .31-2V5l-8-3-3.16 1.18" />
              <path d="M4 14.32V5l8-3v7" />
              <path d="M9.78 21h10.44a2 2 0 0 0 1.8-2.89l-2.94-5.89a2 2 0 0 0-1.8-1.11h-4.56a2 2 0 0 0-1.8 1.11l-2.94 5.89A2 2 0 0 0 9.78 21Z" />
              <path d="M15 12v6" />
            </svg>
            <span>Account Settings</span>
          </div>

          {showBusinessMenu && (
            <div 
              className={`flex items-center gap-2 p-2 rounded cursor-pointer ${activeTab === 'business' ? 'bg-accent text-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}
              onClick={() => handleTabClick('business')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M6 21V5a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v16" />
                <path d="M3 21h18" />
                <path d="M10 9h4" />
                <path d="M10 13h4" />
                <path d="M10 17h4" />
              </svg>
              <span>Business</span>
            </div>
          )}
        </nav>
      </CardContent>
    </Card>
  );
};

export default Sidebar;

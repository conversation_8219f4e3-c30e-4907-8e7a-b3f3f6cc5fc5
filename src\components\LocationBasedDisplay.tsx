import { useState } from 'react';
import { ChevronDown, ChevronRight, MapPin } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";

interface LocationInfo {
  id: string;
  name: string;
  slug: string;
  type: string;
  path: string;
}

interface LocationItem {
  id: string;
  location_id: string;
  location: LocationInfo;
}

interface LocationBasedDisplayProps<T extends LocationItem> {
  items: T[];
  renderItem: (item: T) => React.ReactNode;
  title?: string;
  emptyMessage?: string;
}

function LocationBasedDisplay<T extends LocationItem>({
  items,
  renderItem,
  title = "Location-based Information",
  emptyMessage = "No location-specific data available"
}: LocationBasedDisplayProps<T>) {
  const [expandedItems, setExpandedItems] = useState(new Set<string>());

  const toggleExpanded = (locationId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(locationId)) {
      newExpanded.delete(locationId);
    } else {
      newExpanded.add(locationId);
    }
    setExpandedItems(newExpanded);
  };

  const isExpanded = (locationId: string) => expandedItems.has(locationId);

  if (!items || items.length === 0) {
    return (
      <div className="text-center py-6 text-muted-foreground">
        <MapPin className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p>{emptyMessage}</p>
      </div>
    );
  }

  // Group items by country/region structure if needed
  const groupedItems = items.reduce((acc, item) => {
    const location = item.location;
    const groupKey = location.type === 'county' ? 
      `${location.name}` : // For counties, use the name directly
      `${location.name}`; // For other types, use name as well
    
    if (!acc[groupKey]) {
      acc[groupKey] = [];
    }
    acc[groupKey].push(item);
    return acc;
  }, {} as Record<string, T[]>);

  return (
    <div className="space-y-2">
      {Object.entries(groupedItems).map(([groupName, groupItems]) => (
        <Card key={groupName} className="border-l-4 border-l-blue-500">
          <CardContent className="p-0">
            {/* Location Header */}
            <button
              onClick={() => toggleExpanded(groupName)}
              className="w-full flex items-center justify-between p-3 text-left hover:bg-accent transition-colors"
            >
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-blue-600" />
                <span className="font-medium">{groupName}</span>
                <span className="text-xs text-muted-foreground">
                  ({groupItems.length} item{groupItems.length !== 1 ? 's' : ''})
                </span>
              </div>
              {isExpanded(groupName) ? (
                <ChevronDown className="w-4 h-4 text-muted-foreground" />
              ) : (
                <ChevronRight className="w-4 h-4 text-muted-foreground" />
              )}
            </button>
            
            {/* Location Content */}
            {isExpanded(groupName) && (
              <div className="px-3 pb-3">
                <div className="border-t pt-3 space-y-3">
                  {groupItems.map((item) => (
                    <div key={item.id} className="pl-6">
                      {renderItem(item)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default LocationBasedDisplay;

-- Add user settings columns to profiles table
-- This migration adds profile_visible, newsletter_subscribed, and show_business_menu columns

-- Add the new settings columns
ALTER TABLE public.profiles 
ADD COLUMN profile_visible boolean DEFAULT true,
ADD COLUMN newsletter_subscribed boolean DEFAULT false,
ADD COLUMN show_business_menu boolean DEFAULT true;

-- Update the members_view to only show profiles where profile_visible is true
DROP VIEW IF EXISTS public.members_view;

CREATE VIEW public.members_view AS
SELECT 
  id,
  first_name,
  last_name,
  job_title,
  sustainability_professional,
  avatar_url,
  created_at AS joinedat
FROM public.profiles
WHERE profile_visible = true;

-- Grant permissions on the updated view
GRANT ALL ON TABLE public.members_view TO anon;
GRANT ALL ON TABLE public.members_view TO authenticated;
GRANT ALL ON TABLE public.members_view TO service_role;

-- Add comment explaining the new columns
COMMENT ON COLUMN public.profiles.profile_visible IS 'Controls whether user profile appears in public member directory';
COMMENT ON COLUMN public.profiles.newsletter_subscribed IS 'Whether user wants to receive newsletter emails';
COMMENT ON COLUMN public.profiles.show_business_menu IS 'Whether to show Add Business option in profile sidebar';

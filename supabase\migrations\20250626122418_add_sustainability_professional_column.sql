-- Add sustainability_professional column to profiles table

-- Add the new column with a default value of false
ALTER TABLE public.profiles 
ADD COLUMN sustainability_professional boolean DEFAULT false NOT NULL;

-- Update the members_view to include the new column
DROP VIEW IF EXISTS public.members_view;

CREATE VIEW public.members_view AS
SELECT 
  id,
  first_name,
  last_name,
  job_title,
  sustainability_professional,
  created_at AS joinedat
FROM public.profiles;

-- Grant permissions on the updated view
GRANT ALL ON TABLE public.members_view TO anon;
GRANT ALL ON TABLE public.members_view TO authenticated;
GRANT ALL ON TABLE public.members_view TO service_role;

-- Update the handle_new_user function to include the new field
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.profiles (id, first_name, last_name, job_title, sustainability_professional)
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'job_title', ''),
    COALESCE((NEW.raw_user_meta_data->>'sustainability_professional')::boolean, false)
  );
  RETURN NEW;
END;
$$;

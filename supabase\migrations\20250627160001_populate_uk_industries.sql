-- Populate UK Industries with hierarchical data
-- This migration inserts the 7 main industry categories and their subcategories

-- Insert parent industries first (with sort_order for proper ordering)
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Manufacturing & Production', 'Industries involved in the physical production and manufacturing of goods', 1, NULL),
('Services', 'Service-based industries providing intangible products and professional expertise', 2, NULL),
('Energy & Utilities', 'Industries responsible for energy production, distribution and essential utilities', 3, NULL),
('Primary Industries', 'Industries that extract or harvest natural resources directly from the environment', 4, NULL),
('Construction & Real Estate', 'Industries involved in building, infrastructure development and property management', 5, NULL),
('Healthcare & Education', 'Industries providing health services, education and research capabilities', 6, NULL),
('Social Care', 'Industries providing care and support services for vulnerable populations', 7, NULL);

-- Insert child industries for Manufacturing & Production
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Automotive Manufacturing', 'Production of cars, commercial vehicles, and automotive parts and components', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Aerospace and Defense', 'Manufacturing of aircraft, spacecraft, defense equipment and related technologies', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Pharmaceuticals and Biotechnology', 'Development and production of medicines, medical devices and biotechnology products', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Food and Beverage Production', 'Processing and manufacturing of food products, beverages and agricultural goods', 4, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Textiles and Clothing', 'Manufacturing of fabrics, clothing, footwear and fashion accessories', 5, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Steel and Metals', 'Production and processing of steel, aluminum, copper and other metal products', 6, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL)),
('Chemicals and Petrochemicals', 'Manufacturing of industrial chemicals, plastics, fertilizers and petroleum-based products', 7, 
    (SELECT id FROM public.uk_industries WHERE name = 'Manufacturing & Production' AND parent_id IS NULL));

-- Insert child industries for Services
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Financial Services', 'Banking, insurance, investment management and other financial institutions', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Professional Services', 'Legal services, accounting, consulting, and business advisory services', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Information Technology and Software', 'Software development, IT consulting, cybersecurity and digital services', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Creative Industries', 'Film, television, advertising, design, gaming and digital media production', 4, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Tourism and Hospitality', 'Hotels, restaurants, travel agencies, entertainment venues and leisure services', 5, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Retail and Wholesale Trade', 'Retail: selling directly to consumers through shops, online stores, supermarkets; Wholesale: selling in bulk to other businesses, distributors, retailers', 6, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL)),
('Transportation and Logistics', 'Moving goods and people via road, rail, air, sea and managing supply chains', 7, 
    (SELECT id FROM public.uk_industries WHERE name = 'Services' AND parent_id IS NULL));

-- Insert child industries for Energy & Utilities
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Oil and Gas', 'Extraction, refining and distribution of petroleum products, particularly North Sea operations', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Energy & Utilities' AND parent_id IS NULL)),
('Renewable Energy', 'Wind farms, solar panels, hydroelectric power, biomass and other sustainable energy sources', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Energy & Utilities' AND parent_id IS NULL)),
('Nuclear Power', 'Generation of electricity through nuclear reactors and nuclear fuel processing', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Energy & Utilities' AND parent_id IS NULL)),
('Electricity and Gas Utilities', 'Distribution of electricity and natural gas to homes and businesses through grid networks', 4, 
    (SELECT id FROM public.uk_industries WHERE name = 'Energy & Utilities' AND parent_id IS NULL)),
('Water and Waste Management', 'Water treatment, sewage processing, waste collection, recycling and environmental services', 5, 
    (SELECT id FROM public.uk_industries WHERE name = 'Energy & Utilities' AND parent_id IS NULL));

-- Insert child industries for Primary Industries
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Agriculture and Farming', 'Crop cultivation, livestock farming, dairy production and agricultural management', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Primary Industries' AND parent_id IS NULL)),
('Fishing and Aquaculture', 'Commercial fishing, fish farming, shellfish cultivation and marine resource harvesting', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Primary Industries' AND parent_id IS NULL)),
('Mining and Quarrying', 'Extraction of minerals, coal, stone, sand, gravel and other geological materials', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Primary Industries' AND parent_id IS NULL));

-- Insert child industries for Construction & Real Estate
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Residential and Commercial Construction', 'Building homes, offices, retail spaces, warehouses and other structures', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Construction & Real Estate' AND parent_id IS NULL)),
('Infrastructure Development', 'Construction of roads, railways, bridges, airports, ports, broadband networks, water systems', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Construction & Real Estate' AND parent_id IS NULL)),
('Property Development and Management', 'Real estate development, property investment, facilities management and maintenance', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Construction & Real Estate' AND parent_id IS NULL));

-- Insert child industries for Healthcare & Education
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('National Health Service (NHS)', 'Publicly funded healthcare system providing medical services, hospitals and primary care', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Healthcare & Education' AND parent_id IS NULL)),
('Private Healthcare', 'Private hospitals, clinics, dental practices and specialist medical services', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Healthcare & Education' AND parent_id IS NULL)),
('Education', 'Universities, colleges, schools, training providers and educational support services', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Healthcare & Education' AND parent_id IS NULL)),
('Research and Development', 'Scientific research, innovation centers, technology development and academic research', 4, 
    (SELECT id FROM public.uk_industries WHERE name = 'Healthcare & Education' AND parent_id IS NULL));

-- Insert child industries for Social Care
INSERT INTO public.uk_industries (name, description, sort_order, parent_id) VALUES
('Elderly Care', 'Residential care homes, nursing homes, home care services and day centers for older adults', 1, 
    (SELECT id FROM public.uk_industries WHERE name = 'Social Care' AND parent_id IS NULL)),
('Child and Family Services', 'Childcare, fostering, adoption services, family support and child protection services', 2, 
    (SELECT id FROM public.uk_industries WHERE name = 'Social Care' AND parent_id IS NULL)),
('Disability Support Services', 'Services for people with physical, learning or mental health disabilities including supported living', 3, 
    (SELECT id FROM public.uk_industries WHERE name = 'Social Care' AND parent_id IS NULL)),
('Mental Health Services', 'Counseling, therapy, psychiatric services and community mental health support', 4, 
    (SELECT id FROM public.uk_industries WHERE name = 'Social Care' AND parent_id IS NULL)),
('Housing and Homelessness Support', 'Social housing, homeless shelters, housing associations and supported accommodation', 5, 
    (SELECT id FROM public.uk_industries WHERE name = 'Social Care' AND parent_id IS NULL));

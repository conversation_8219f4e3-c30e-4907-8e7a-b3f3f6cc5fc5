import React from 'react';
import { Building } from 'lucide-react';

interface BusinessLogoDisplayProps {
  logoUrl?: string | null;
  businessName?: string | null;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  shape?: 'circle' | 'square';
}

const BusinessLogoDisplay: React.FC<BusinessLogoDisplayProps> = ({
  logoUrl,
  businessName,
  size = 'md',
  className = '',
  shape = 'square',
}) => {
  // Generate initials for fallback
  const getInitials = () => {
    if (businessName) {
      const words = businessName.trim().split(' ');
      if (words.length >= 2) {
        return `${words[0].charAt(0)}${words[1].charAt(0)}`.toUpperCase();
      }
      return businessName.charAt(0).toUpperCase();
    }
    return 'B';
  };

  // Size mappings with mobile responsive sizing
  const sizeClasses = {
    sm: 'h-8 w-8 sm:h-12 sm:w-12',
    md: 'h-12 w-12 sm:h-20 sm:w-20',
    lg: 'h-20 w-20 sm:h-32 sm:w-32',
    xl: 'h-24 w-24 sm:h-36 sm:w-36 lg:h-40 lg:w-40',
  };

  const textSizeClasses = {
    sm: 'text-xs sm:text-base',
    md: 'text-sm sm:text-lg',
    lg: 'text-lg sm:text-2xl',
    xl: 'text-xl sm:text-3xl lg:text-4xl',
  };

  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-lg',
  };

  const sizeClass = sizeClasses[size];
  const textSizeClass = textSizeClasses[size];
  const shapeClass = shapeClasses[shape];

  return (
    <div className={`${sizeClass} ${shapeClass} ${className} border bg-muted flex items-center justify-center overflow-hidden flex-shrink-0`}>
      {logoUrl ? (
        <img 
          src={logoUrl} 
          alt={`${businessName || 'Business'} logo`}
          className="h-full w-full object-cover"
        />
      ) : (
        <div className="flex flex-col items-center justify-center text-muted-foreground">
          <Building className={`${size === 'sm' ? 'h-3 w-3' : size === 'md' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-8 w-8'} mb-1`} />
          <span className={`${textSizeClass} font-medium`}>{getInitials()}</span>
        </div>
      )}
    </div>
  );
};

export default BusinessLogoDisplay;

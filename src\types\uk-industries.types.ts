// UK Industries types for the Net Zero Nexus Hub
import { Database } from '@/integrations/supabase/types';

// Database table types
export type UKIndustry = Database['public']['Tables']['uk_industries']['Row'];
export type ProfileIndustry = Database['public']['Tables']['profile_industry']['Row'];
export type BusinessIndustry = Database['public']['Tables']['business_industry']['Row'];
export type BusinessTargetIndustry = Database['public']['Tables']['business_target_industries']['Row'];

// Insert types for creating new records
export type NewUKIndustry = Database['public']['Tables']['uk_industries']['Insert'];
export type NewProfileIndustry = Database['public']['Tables']['profile_industry']['Insert'];
export type NewBusinessIndustry = Database['public']['Tables']['business_industry']['Insert'];
export type NewBusinessTargetIndustry = Database['public']['Tables']['business_target_industries']['Insert'];

// Extended types with relationships
export interface UKIndustryWithChildren extends UKIndustry {
  children: UKIndustry[];
}

export interface UKIndustryWithParent extends UKIndustry {
  parent: UKIndustry | null;
}

export interface ProfileWithIndustry {
  id: string;
  first_name: string | null;
  last_name: string | null;
  industry: UKIndustryWithParent | null;
}

export interface BusinessWithIndustries {
  id: string;
  business_name: string;
  main_industry: UKIndustryWithParent | null;
  target_industries: UKIndustryWithParent[];
}

// Form data types
export interface IndustrySelection {
  industryId: string;
  parentId: string | null;
  industryName: string;
  parentName?: string;
}

export interface ProfileIndustryFormData {
  selectedIndustryId: string | null; // Single select for user's industry
}

export interface BusinessIndustryFormData {
  selectedIndustryId: string | null; // Single select for business's main industry
  selectedTargetIndustryIds: string[]; // Multi-select for target industries
}

// Hierarchical structure for UI components
export interface IndustryHierarchy {
  [parentId: string]: {
    id: string;
    name: string;
    description: string | null;
    sort_order: number;
    children: {
      [childId: string]: {
        id: string;
        name: string;
        description: string | null;
        sort_order: number;
      };
    };
  };
}

// Selection state for components
export interface IndustrySelectionState {
  selectedIndustry: string | null; // For single select
  selectedTargetIndustries: Set<string>; // For multi-select
  expandedCategories: Set<string>;
}

// API response types
export interface IndustriesResponse {
  industries: UKIndustryWithChildren[];
}

export interface UserIndustryResponse {
  industry: UKIndustryWithParent | null;
}

export interface BusinessIndustriesResponse {
  main_industry: UKIndustryWithParent | null;
  target_industries: UKIndustryWithParent[];
}

// Constants for the 7 main industry categories
export const UK_INDUSTRY_CATEGORIES = {
  MANUFACTURING_PRODUCTION: 'Manufacturing & Production',
  SERVICES: 'Services',
  ENERGY_UTILITIES: 'Energy & Utilities',
  PRIMARY_INDUSTRIES: 'Primary Industries',
  CONSTRUCTION_REAL_ESTATE: 'Construction & Real Estate',
  HEALTHCARE_EDUCATION: 'Healthcare & Education',
  SOCIAL_CARE: 'Social Care'
} as const;

export type UKIndustryCategoryName = typeof UK_INDUSTRY_CATEGORIES[keyof typeof UK_INDUSTRY_CATEGORIES];

// Utility types for filtering and searching
export interface IndustryFilter {
  parentIndustryId?: string;
  searchTerm?: string;
  includeChildren?: boolean;
}

export interface IndustrySearchResult {
  industry: UKIndustryWithParent;
  matchType: 'name' | 'description' | 'parent';
}

// Display options for components
export interface IndustryDisplayOptions {
  showParent?: boolean;
  showDescription?: boolean;
  variant?: 'badge' | 'card' | 'inline' | 'list';
  maxDisplay?: number;
  separator?: string;
}

// Industry statistics (for future analytics)
export interface IndustryStats {
  industryId: string;
  industryName: string;
  userCount: number;
  businessCount: number;
  targetBusinessCount: number;
}

// Validation schemas (for form validation)
export interface IndustryValidation {
  required?: boolean;
  allowParentSelection?: boolean; // Whether parent industries can be selected
  maxTargetIndustries?: number;
  excludeIndustries?: string[]; // Industry IDs to exclude from selection
}

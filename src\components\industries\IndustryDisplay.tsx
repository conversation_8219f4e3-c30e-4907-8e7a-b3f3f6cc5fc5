import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, Star } from 'lucide-react';
import type { UKIndustryWithParent } from '@/types/uk-industries.types';

interface IndustryDisplayProps {
  industry?: UKIndustryWithParent | null;
  targetIndustries?: UKIndustryWithParent[];
  title?: string;
  description?: string;
  showParent?: boolean;
  variant?: 'card' | 'inline' | 'badge' | 'list';
  className?: string;
}

export const IndustryDisplay: React.FC<IndustryDisplayProps> = ({
  industry,
  targetIndustries = [],
  title = "Industry Information",
  description,
  showParent = true,
  variant = 'card',
  className = ""
}) => {
  const hasMainIndustry = !!industry;
  const hasTargetIndustries = targetIndustries.length > 0;

  if (!hasMainIndustry && !hasTargetIndustries) {
    return null;
  }

  const renderIndustryBadge = (ind: UKIndustryWithParent, isMain = false) => (
    <Badge
      key={ind.id}
      variant={isMain ? "default" : "secondary"}
      className={`${isMain ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
    >
      {isMain && <Star className="w-3 h-3 mr-1" />}
      {showParent && ind.parent ? `${ind.parent.name} > ${ind.name}` : ind.name}
    </Badge>
  );

  const renderIndustryList = () => (
    <div className="space-y-3">
      {hasMainIndustry && (
        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-2 flex items-center gap-2">
            <Building2 className="w-4 h-4" />
            Main Industry
          </h4>
          <div className="ml-6">
            {renderIndustryBadge(industry!, true)}
          </div>
        </div>
      )}

      {hasTargetIndustries && (
        <div>
          <h4 className="font-medium text-sm text-muted-foreground mb-2 flex items-center gap-2">
            <Building2 className="w-4 h-4" />
            Target Industries
          </h4>
          <div className="flex flex-wrap gap-2 ml-6">
            {targetIndustries.map((targetIndustry) => renderIndustryBadge(targetIndustry))}
          </div>
        </div>
      )}
    </div>
  );

  const renderInlineBadges = () => (
    <div className="flex flex-wrap gap-2">
      {hasMainIndustry && renderIndustryBadge(industry!, true)}
      {targetIndustries.map((targetIndustry) => renderIndustryBadge(targetIndustry))}
    </div>
  );

  if (variant === 'badge') {
    return (
      <div className={className}>
        {hasMainIndustry && renderIndustryBadge(industry!, true)}
      </div>
    );
  }

  if (variant === 'inline') {
    return (
      <div className={className}>
        <div className="space-y-3">
          {title && (
            <h3 className="font-medium flex items-center gap-2">
              <Building2 className="w-4 h-4 text-blue-600" />
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
          {renderIndustryList()}
        </div>
      </div>
    );
  }

  if (variant === 'list') {
    return (
      <div className={className}>
        {renderIndustryList()}
      </div>
    );
  }

  // Default card variant
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="w-5 h-5 text-blue-600" />
          {title}
        </CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {renderIndustryList()}
      </CardContent>
    </Card>
  );
};

// Simplified component for just showing industry name as text
interface IndustryTextProps {
  industry?: UKIndustryWithParent | null;
  targetIndustries?: UKIndustryWithParent[];
  showParent?: boolean;
  separator?: string;
  maxDisplay?: number;
  className?: string;
}

export const IndustryText: React.FC<IndustryTextProps> = ({
  industry,
  targetIndustries = [],
  showParent = true,
  separator = ', ',
  maxDisplay,
  className = ""
}) => {
  const allIndustries = [];
  
  if (industry) {
    allIndustries.push(industry);
  }
  
  allIndustries.push(...targetIndustries);

  if (allIndustries.length === 0) {
    return <span className={className}>No industry specified</span>;
  }

  let displayIndustries = allIndustries;
  if (maxDisplay && allIndustries.length > maxDisplay) {
    displayIndustries = allIndustries.slice(0, maxDisplay);
  }

  const industryText = displayIndustries.map((ind, index) => {
    const isMain = index === 0 && !!industry;
    const name = showParent && ind.parent ? `${ind.parent.name} > ${ind.name}` : ind.name;
    return isMain ? `${name} (Main)` : name;
  }).join(separator);

  const remainingCount = allIndustries.length - displayIndustries.length;

  return (
    <span className={className}>
      {industryText}
      {remainingCount > 0 && ` and ${remainingCount} more`}
    </span>
  );
};

// Hook for getting industry display data
export const useIndustryDisplay = (
  industry?: UKIndustryWithParent | null,
  targetIndustries: UKIndustryWithParent[] = []
) => {
  const hasMainIndustry = !!industry;
  const hasTargetIndustries = targetIndustries.length > 0;
  const totalCount = (hasMainIndustry ? 1 : 0) + targetIndustries.length;
  
  const mainIndustryName = industry?.name;
  const mainIndustryFullName = industry && industry.parent 
    ? `${industry.parent.name} > ${industry.name}` 
    : industry?.name;
  
  const targetIndustryNames = targetIndustries.map(ind => ind.name);
  const targetIndustryFullNames = targetIndustries.map(ind => 
    ind.parent ? `${ind.parent.name} > ${ind.name}` : ind.name
  );

  const summary = {
    hasMainIndustry,
    hasTargetIndustries,
    totalCount,
    mainIndustryName,
    mainIndustryFullName,
    targetIndustryNames,
    targetIndustryFullNames,
    hasMultipleIndustries: totalCount > 1
  };

  return summary;
};

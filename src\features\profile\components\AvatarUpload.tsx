import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { cloudflareService } from '@/services/cloudflareService';
import { Upload, X, User } from 'lucide-react';

interface AvatarUploadProps {
  currentAvatarUrl?: string | null;
  currentAvatarCloudflareKey?: string | null;
  onAvatarUpdate: (url: string | null, key: string | null) => void;
  userId: string;
  firstName?: string;
  lastName?: string;
}

const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatarUrl,
  currentAvatarCloudflareKey,
  onAvatarUpdate,
  userId,
  firstName,
  lastName,
}) => {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatarUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Generate initials for fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);

      // Create preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Delete old avatar from Cloudflare R2 if it exists
      if (currentAvatarCloudflareKey) {
        try {
          await cloudflareService.deleteAvatar(currentAvatarCloudflareKey);
        } catch (deleteError) {
          console.error('Error deleting old avatar:', deleteError);
          // Continue with upload even if deletion fails
        }
      }

      // Upload new avatar to Cloudflare
      const result = await cloudflareService.uploadAvatar(file, userId);

      // Clean up preview URL
      URL.revokeObjectURL(objectUrl);

      // Update with real URL
      setPreviewUrl(result.url);
      onAvatarUpdate(result.url, result.key);

      toast({
        title: "Avatar ready",
        description: "Click 'Save Changes' to update your profile",
      });

    } catch (error) {
      // Clean up preview URL on error
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      
      // Revert preview
      setPreviewUrl(currentAvatarUrl || null);

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload avatar",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveAvatar = () => {
    setPreviewUrl(null);
    onAvatarUpdate(null, null);
    
    toast({
      title: "Avatar removed",
      description: "Click 'Save Changes' to update your profile",
    });
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        {/* Avatar Display */}
        <div className="relative">
          <Avatar className="h-20 w-20">
            <AvatarImage src={previewUrl || undefined} alt="Profile avatar" />
            <AvatarFallback className="text-lg font-medium bg-muted">
              {previewUrl ? '' : getInitials()}
            </AvatarFallback>
          </Avatar>
          
          {/* Remove button for existing avatar */}
          {previewUrl && !uploading && (
            <button
              onClick={handleRemoveAvatar}
              className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 hover:bg-destructive/90 transition-colors"
              title="Remove avatar"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex-1 space-y-2">
          <div>
            <Label htmlFor="avatar-upload" className="text-sm font-medium">
              Profile Avatar
            </Label>
            <p className="text-xs text-muted-foreground">
              Upload a photo to personalize your profile
            </p>
          </div>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={triggerFileInput}
              disabled={uploading}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>{uploading ? 'Uploading...' : 'Upload Photo'}</span>
            </Button>

            {previewUrl && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveAvatar}
                disabled={uploading}
              >
                Remove
              </Button>
            )}
          </div>

          <p className="text-xs text-muted-foreground">
            Supports JPEG, PNG, WebP, GIF. Max size: 5MB
          </p>
        </div>
      </div>

      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        onChange={handleFileSelect}
        className="hidden"
        disabled={uploading}
      />

      {/* Upload Progress/Status */}
      {uploading && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Uploading avatar...</span>
        </div>
      )}
    </div>
  );
};

export default AvatarUpload;

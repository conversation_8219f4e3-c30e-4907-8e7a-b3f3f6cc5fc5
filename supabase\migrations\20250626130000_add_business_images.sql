-- Add image support to businesses table
-- This migration adds logo and product images support

-- Add the new image columns
ALTER TABLE public.businesses 
ADD COLUMN logo_url text,
ADD COLUMN logo_cloudflare_key text,
ADD COLUMN product_images jsonb DEFAULT '[]'::jsonb;

-- Add comment for documentation
COMMENT ON COLUMN public.businesses.logo_url IS 'Public URL for the business logo image';
COMMENT ON COLUMN public.businesses.logo_cloudflare_key IS 'Cloudflare R2 key for logo deletion - handled directly in application';
COMMENT ON COLUMN public.businesses.product_images IS 'JSON array of product images with metadata';

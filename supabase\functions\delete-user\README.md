# Delete User Edge Function

This edge function allows users to securely delete their own accounts using the Supabase service role key.

## Setup

1. Make sure you have the Supabase CLI installed:
   ```bash
   npm install -g supabase
   ```

2. Deploy the function:
   ```bash
   supabase functions deploy delete-user
   ```

3. Set up environment variables in your Supabase dashboard:
   - `SUPABASE_URL`: Your Supabase project URL
   - `SUPABASE_SERVICE_ROLE_KEY`: Your service role key (found in API settings)

## Usage

The function is called automatically from the Profile page when a user clicks "Delete Account". It:

1. Verifies the user's authentication token
2. Deletes the user's profile data from the database
3. Deletes the user from the auth system
4. Returns a success response

## Security

- Uses the service role key which is kept secure on the server
- Verifies the user's JWT token before deletion
- Only allows users to delete their own accounts

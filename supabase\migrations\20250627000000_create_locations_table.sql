-- Create the locations table with hierarchical structure
CREATE TABLE locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('country', 'region', 'county')),
    parent_id UUID REFERENCES locations(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_locations_parent_id ON locations(parent_id);
CREATE INDEX idx_locations_type ON locations(type);
CREATE INDEX idx_locations_slug ON locations(slug);
CREATE UNIQUE INDEX idx_locations_slug_unique ON locations(slug);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- <PERSON>reate trigger to automatically update updated_at
CREATE TRIGGER update_locations_updated_at BEFORE UPDATE ON locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert Countries
INSERT INTO locations (name, slug, type, sort_order) VALUES
('England', 'england', 'country', 1),
('Scotland', 'scotland', 'country', 2),
('Wales', 'wales', 'country', 3),
('Northern Ireland', 'northern-ireland', 'country', 4);

-- Insert England Regions
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('East of England', 'east-of-england', 'region', (SELECT id FROM locations WHERE slug = 'england'), 1),
('East Midlands', 'east-midlands', 'region', (SELECT id FROM locations WHERE slug = 'england'), 2),
('Greater London', 'greater-london', 'region', (SELECT id FROM locations WHERE slug = 'england'), 3),
('North East England', 'north-east-england', 'region', (SELECT id FROM locations WHERE slug = 'england'), 4),
('North West England', 'north-west-england', 'region', (SELECT id FROM locations WHERE slug = 'england'), 5),
('South East England', 'south-east-england', 'region', (SELECT id FROM locations WHERE slug = 'england'), 6),
('South West England', 'south-west-england', 'region', (SELECT id FROM locations WHERE slug = 'england'), 7),
('West Midlands', 'west-midlands', 'region', (SELECT id FROM locations WHERE slug = 'england'), 8),
('Yorkshire and the Humber', 'yorkshire-and-the-humber', 'region', (SELECT id FROM locations WHERE slug = 'england'), 9);

-- Insert Scotland Region
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Historic Counties', 'historic-counties-scotland', 'region', (SELECT id FROM locations WHERE slug = 'scotland'), 1);

-- Insert Wales Region
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Principal Areas', 'principal-areas', 'region', (SELECT id FROM locations WHERE slug = 'wales'), 1);

-- Insert Northern Ireland Region
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Historic Counties', 'historic-counties-northern-ireland', 'region', (SELECT id FROM locations WHERE slug = 'northern-ireland'), 1);

-- Insert East of England Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Bedfordshire', 'bedfordshire', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 1),
('Cambridgeshire', 'cambridgeshire', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 2),
('Essex', 'essex', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 3),
('Hertfordshire', 'hertfordshire', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 4),
('Norfolk', 'norfolk', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 5),
('Suffolk', 'suffolk', 'county', (SELECT id FROM locations WHERE slug = 'east-of-england'), 6);

-- Insert East Midlands Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Derbyshire', 'derbyshire', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 1),
('Leicestershire', 'leicestershire', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 2),
('Lincolnshire', 'lincolnshire', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 3),
('Northamptonshire', 'northamptonshire', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 4),
('Nottinghamshire', 'nottinghamshire', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 5),
('Rutland', 'rutland', 'county', (SELECT id FROM locations WHERE slug = 'east-midlands'), 6);

-- Insert Greater London County
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Greater London', 'greater-london-county', 'county', (SELECT id FROM locations WHERE slug = 'greater-london'), 1);

-- Insert North East England Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('County Durham', 'county-durham', 'county', (SELECT id FROM locations WHERE slug = 'north-east-england'), 1),
('Northumberland', 'northumberland', 'county', (SELECT id FROM locations WHERE slug = 'north-east-england'), 2),
('Tyne and Wear', 'tyne-and-wear', 'county', (SELECT id FROM locations WHERE slug = 'north-east-england'), 3);

-- Insert North West England Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Cheshire', 'cheshire', 'county', (SELECT id FROM locations WHERE slug = 'north-west-england'), 1),
('Cumbria', 'cumbria', 'county', (SELECT id FROM locations WHERE slug = 'north-west-england'), 2),
('Greater Manchester', 'greater-manchester', 'county', (SELECT id FROM locations WHERE slug = 'north-west-england'), 3),
('Lancashire', 'lancashire', 'county', (SELECT id FROM locations WHERE slug = 'north-west-england'), 4),
('Merseyside', 'merseyside', 'county', (SELECT id FROM locations WHERE slug = 'north-west-england'), 5);

-- Insert South East England Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Berkshire', 'berkshire', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 1),
('Buckinghamshire', 'buckinghamshire', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 2),
('East Sussex', 'east-sussex', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 3),
('Hampshire', 'hampshire', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 4),
('Isle of Wight', 'isle-of-wight', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 5),
('Kent', 'kent', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 6),
('Oxfordshire', 'oxfordshire', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 7),
('Surrey', 'surrey', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 8),
('West Sussex', 'west-sussex', 'county', (SELECT id FROM locations WHERE slug = 'south-east-england'), 9);

-- Insert South West England Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Bristol', 'bristol', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 1),
('Cornwall', 'cornwall', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 2),
('Devon', 'devon', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 3),
('Dorset', 'dorset', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 4),
('Gloucestershire', 'gloucestershire', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 5),
('Somerset', 'somerset', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 6),
('Wiltshire', 'wiltshire', 'county', (SELECT id FROM locations WHERE slug = 'south-west-england'), 7);

-- Insert West Midlands Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Herefordshire', 'herefordshire', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 1),
('Shropshire', 'shropshire', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 2),
('Staffordshire', 'staffordshire', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 3),
('Warwickshire', 'warwickshire', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 4),
('West Midlands', 'west-midlands-county', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 5),
('Worcestershire', 'worcestershire', 'county', (SELECT id FROM locations WHERE slug = 'west-midlands'), 6);

-- Insert Yorkshire and the Humber Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('East Riding of Yorkshire', 'east-riding-of-yorkshire', 'county', (SELECT id FROM locations WHERE slug = 'yorkshire-and-the-humber'), 1),
('North Yorkshire', 'north-yorkshire', 'county', (SELECT id FROM locations WHERE slug = 'yorkshire-and-the-humber'), 2),
('South Yorkshire', 'south-yorkshire', 'county', (SELECT id FROM locations WHERE slug = 'yorkshire-and-the-humber'), 3),
('West Yorkshire', 'west-yorkshire', 'county', (SELECT id FROM locations WHERE slug = 'yorkshire-and-the-humber'), 4);

-- Insert Scotland Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Aberdeenshire', 'aberdeenshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 1),
('Angus (Forfarshire)', 'angus-forfarshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 2),
('Argyll', 'argyll', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 3),
('Ayrshire', 'ayrshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 4),
('Banffshire', 'banffshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 5),
('Berwickshire', 'berwickshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 6),
('Bute', 'bute', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 7),
('Caithness', 'caithness', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 8),
('Clackmannanshire', 'clackmannanshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 9),
('Dumfriesshire', 'dumfriesshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 10),
('Dunbartonshire', 'dunbartonshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 11),
('East Lothian (Haddingtonshire)', 'east-lothian-haddingtonshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 12),
('Fife', 'fife', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 13),
('Inverness-shire', 'inverness-shire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 14),
('Kincardineshire', 'kincardineshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 15),
('Kinross-shire', 'kinross-shire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 16),
('Kirkcudbrightshire', 'kirkcudbrightshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 17),
('Lanarkshire', 'lanarkshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 18),
('Midlothian (Edinburghshire)', 'midlothian-edinburghshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 19),
('Moray (Elginshire)', 'moray-elginshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 20),
('Nairnshire', 'nairnshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 21),
('Orkney', 'orkney', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 22),
('Peeblesshire', 'peeblesshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 23),
('Perthshire', 'perthshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 24),
('Renfrewshire', 'renfrewshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 25),
('Ross and Cromarty', 'ross-and-cromarty', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 26),
('Roxburghshire', 'roxburghshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 27),
('Selkirkshire', 'selkirkshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 28),
('Shetland (Zetland)', 'shetland-zetland', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 29),
('Stirlingshire', 'stirlingshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 30),
('Sutherland', 'sutherland', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 31),
('West Lothian (Linlithgowshire)', 'west-lothian-linlithgowshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 32),
('Wigtownshire', 'wigtownshire', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-scotland'), 33);

-- Insert Wales Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Anglesey (Isle of Anglesey)', 'anglesey-isle-of-anglesey', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 1),
('Blaenau Gwent', 'blaenau-gwent', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 2),
('Bridgend', 'bridgend', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 3),
('Caerphilly', 'caerphilly', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 4),
('Cardiff', 'cardiff', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 5),
('Carmarthenshire', 'carmarthenshire', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 6),
('Ceredigion', 'ceredigion', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 7),
('Conwy', 'conwy', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 8),
('Denbighshire', 'denbighshire', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 9),
('Flintshire', 'flintshire', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 10),
('Gwynedd', 'gwynedd', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 11),
('Merthyr Tydfil', 'merthyr-tydfil', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 12),
('Monmouthshire', 'monmouthshire', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 13),
('Neath Port Talbot', 'neath-port-talbot', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 14),
('Newport', 'newport', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 15),
('Pembrokeshire', 'pembrokeshire', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 16),
('Powys', 'powys', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 17),
('Rhondda Cynon Taf', 'rhondda-cynon-taf', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 18),
('Swansea', 'swansea', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 19),
('Torfaen', 'torfaen', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 20),
('Vale of Glamorgan', 'vale-of-glamorgan', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 21),
('Wrexham', 'wrexham', 'county', (SELECT id FROM locations WHERE slug = 'principal-areas'), 22);

-- Insert Northern Ireland Counties
INSERT INTO locations (name, slug, type, parent_id, sort_order) VALUES
('Antrim', 'antrim', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 1),
('Armagh', 'armagh', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 2),
('Down', 'down', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 3),
('Fermanagh', 'fermanagh', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 4),
('Londonderry', 'londonderry', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 5),
('Tyrone', 'tyrone', 'county', (SELECT id FROM locations WHERE slug = 'historic-counties-northern-ireland'), 6);

-- Useful queries for working with the hierarchical data

-- View all locations with their hierarchy
CREATE OR REPLACE VIEW locations_hierarchy AS
WITH RECURSIVE location_tree AS (
    -- Base case: root locations (countries)
    SELECT 
        id,
        name,
        slug,
        type,
        parent_id,
        sort_order,
        name as full_path,
        0 as level
    FROM locations 
    WHERE parent_id IS NULL
    
    UNION ALL
    
    -- Recursive case: children
    SELECT 
        l.id,
        l.name,
        l.slug,
        l.type,
        l.parent_id,
        l.sort_order,
        lt.full_path || ' > ' || l.name as full_path,
        lt.level + 1 as level
    FROM locations l
    INNER JOIN location_tree lt ON l.parent_id = lt.id
)
SELECT * FROM location_tree
ORDER BY full_path;

-- Function to get all children of a location
CREATE OR REPLACE FUNCTION get_location_children(location_id UUID)
RETURNS TABLE(
    id UUID,
    name TEXT,
    slug TEXT,
    type TEXT,
    level INTEGER
) AS $$
WITH RECURSIVE children AS (
    SELECT l.id, l.name, l.slug, l.type, 0 as level
    FROM locations l
    WHERE l.id = location_id
    
    UNION ALL
    
    SELECT l.id, l.name, l.slug, l.type, c.level + 1
    FROM locations l
    INNER JOIN children c ON l.parent_id = c.id
)
SELECT c.id, c.name, c.slug, c.type, c.level
FROM children c
WHERE c.level > 0
ORDER BY c.level, c.name;
$$ LANGUAGE sql;

-- Function to get the full path of a location
CREATE OR REPLACE FUNCTION get_location_path(location_id UUID)
RETURNS TEXT AS $$
WITH RECURSIVE path AS (
    SELECT l.id, l.name, l.parent_id, l.name as full_path
    FROM locations l
    WHERE l.id = location_id
    
    UNION ALL
    
    SELECT l.id, l.name, l.parent_id, l.name || ' > ' || p.full_path
    FROM locations l
    INNER JOIN path p ON l.id = p.parent_id
)
SELECT full_path
FROM path
WHERE parent_id IS NULL;
$$ LANGUAGE sql;

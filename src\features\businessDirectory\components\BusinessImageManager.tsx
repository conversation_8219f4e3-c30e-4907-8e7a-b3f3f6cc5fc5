import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { cloudflareService } from '@/services/cloudflareService';
import { supabase } from '@/integrations/supabase/client';
import { Upload, X, Image as ImageIcon, Loader2 } from 'lucide-react';
import type { ProductImage } from '@/types/business-images.types';
import type { Database } from '@/types/database.types';

type Business = Database['public']['Tables']['businesses']['Row'];

interface BusinessImageManagerProps {
  business: Business;
  onImagesUpdated: () => void;
}

const BusinessImageManager: React.FC<BusinessImageManagerProps> = ({ 
  business, 
  onImagesUpdated 
}) => {
  const [logoUploading, setLogoUploading] = useState(false);
  const [productUploading, setProductUploading] = useState(false);
  const [newProductDescription, setNewProductDescription] = useState('');
  const [uploadingImage, setUploadingImage] = useState<string | null>(null);
  const [localProductImages, setLocalProductImages] = useState<ProductImage[]>([]);
  const { toast } = useToast();

  // Parse product images from JSON and initialize local state
  const productImages: ProductImage[] = business.product_images 
    ? (typeof business.product_images === 'string' 
        ? JSON.parse(business.product_images) 
        : business.product_images as unknown as ProductImage[])
    : [];

  // Update local state when business data changes
  React.useEffect(() => {
    setLocalProductImages(productImages);
  }, [business.product_images]);

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLogoUploading(true);
    try {
      // Delete old logo if it exists
      if (business.logo_cloudflare_key) {
        await cloudflareService.deleteBusinessImage(business.logo_cloudflare_key);
      }

      // Upload new logo
      const { url, key } = await cloudflareService.uploadBusinessLogo(file, business.id);

      // Update business in database
      const { error } = await supabase
        .from('businesses')
        .update({
          logo_url: url,
          logo_cloudflare_key: key,
        } as any)
        .eq('id', business.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Logo uploaded successfully",
      });

      onImagesUpdated();
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast({
        title: "Error",
        description: `Failed to upload logo: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setLogoUploading(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const handleProductImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Create preview URL for immediate display
    const previewUrl = URL.createObjectURL(file);
    setUploadingImage(previewUrl);
    setProductUploading(true);

    try {
      toast({
        title: "Uploading...",
        description: "Your product image is being uploaded to the cloud",
      });

      // Upload product image
      const { url, key } = await cloudflareService.uploadProductImage(file, business.id);

      // Create new product image object
      const newImage: ProductImage = {
        id: `img_${Date.now()}`,
        url,
        cloudflareKey: key,
        filename: file.name,
        description: newProductDescription.trim() || undefined,
        uploadedAt: new Date().toISOString(),
        order: localProductImages.length,
      };

      // Add to existing product images
      const updatedImages = [...localProductImages, newImage];

      // Update business in database
      const { error } = await supabase
        .from('businesses')
        .update({
          product_images: JSON.stringify(updatedImages),
        } as any)
        .eq('id', business.id);

      if (error) throw error;

      // Update local state immediately
      setLocalProductImages(updatedImages);

      toast({
        title: "Success!",
        description: "Product image uploaded and saved successfully",
      });

      setNewProductDescription('');
      onImagesUpdated();
    } catch (error) {
      console.error('Error uploading product image:', error);
      toast({
        title: "Upload Failed",
        description: `Failed to upload product image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setProductUploading(false);
      setUploadingImage(null);
      // Clean up preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
      // Reset file input
      event.target.value = '';
    }
  };

  const handleRemoveLogo = async () => {
    if (!business.logo_cloudflare_key) return;

    try {
      // Delete from Cloudflare
      await cloudflareService.deleteBusinessImage(business.logo_cloudflare_key);

      // Update business in database
      const { error } = await supabase
        .from('businesses')
        .update({
          logo_url: null,
          logo_cloudflare_key: null,
        } as any)
        .eq('id', business.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Logo removed successfully",
      });

      onImagesUpdated();
    } catch (error) {
      console.error('Error removing logo:', error);
      toast({
        title: "Error",
        description: "Failed to remove logo",
        variant: "destructive",
      });
    }
  };

  const handleRemoveProductImage = async (imageId: string) => {
    const imageToRemove = localProductImages.find(img => img.id === imageId);
    if (!imageToRemove) return;

    try {
      // Delete from Cloudflare
      await cloudflareService.deleteBusinessImage(imageToRemove.cloudflareKey);

      // Remove from array and update order
      const updatedImages = localProductImages
        .filter(img => img.id !== imageId)
        .map((img, index) => ({ ...img, order: index }));

      // Update business in database
      const { error } = await supabase
        .from('businesses')
        .update({
          product_images: JSON.stringify(updatedImages),
        } as any)
        .eq('id', business.id);

      if (error) throw error;

      // Update local state immediately
      setLocalProductImages(updatedImages);

      toast({
        title: "Success",
        description: "Product image removed successfully",
      });

      onImagesUpdated();
    } catch (error) {
      console.error('Error removing product image:', error);
      toast({
        title: "Error",
        description: "Failed to remove product image",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Logo Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Business Logo
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {business.logo_url ? (
            <div className="space-y-4">
              <div className="relative inline-block">
                <img
                  src={business.logo_url}
                  alt="Business logo"
                  className="h-32 w-32 object-cover rounded-lg border"
                />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute -top-2 -right-2 h-6 w-6"
                  onClick={handleRemoveLogo}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-600">No logo uploaded</p>
            </div>
          )}
          
          <div className="flex items-center gap-4">
            <Label htmlFor="logo-upload" className="cursor-pointer">
              <div className="flex items-center gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  disabled={logoUploading}
                  asChild
                >
                  <span>
                    {logoUploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    {business.logo_url ? 'Replace Logo' : 'Upload Logo'}
                  </span>
                </Button>
              </div>
            </Label>
            <Input
              id="logo-upload"
              type="file"
              accept="image/*"
              onChange={handleLogoUpload}
              className="hidden"
              disabled={logoUploading}
            />
          </div>
        </CardContent>
      </Card>

      {/* Product Images Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5" />
            Product Images
            <Badge variant="secondary">{localProductImages.length}/10</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {(localProductImages.length > 0 || uploadingImage) && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {localProductImages.map((image) => (
                <div key={image.id} className="relative group">
                  <img
                    src={image.url}
                    alt={image.description || image.filename}
                    className="h-24 w-24 object-cover rounded-lg border"
                  />
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute -top-2 -right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleRemoveProductImage(image.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                  {image.description && (
                    <p className="text-xs text-gray-600 mt-1 truncate">
                      {image.description}
                    </p>
                  )}
                </div>
              ))}
              {uploadingImage && (
                <div className="relative">
                  <img
                    src={uploadingImage}
                    alt="Uploading..."
                    className="h-24 w-24 object-cover rounded-lg border opacity-70"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
                    <Loader2 className="h-6 w-6 animate-spin text-white" />
                  </div>
                  {newProductDescription && (
                    <p className="text-xs text-gray-600 mt-1 truncate">
                      {newProductDescription}
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {localProductImages.length < 10 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="product-description">Product Description (Optional)</Label>
                <Textarea
                  id="product-description"
                  placeholder="Describe this product image..."
                  value={newProductDescription}
                  onChange={(e) => setNewProductDescription(e.target.value)}
                  rows={2}
                />
              </div>
              
              <div className="flex items-center gap-4">
                <Label htmlFor="product-upload" className="cursor-pointer">
                  <div className="flex items-center gap-2">
                    <Button 
                      type="button" 
                      variant="outline" 
                      disabled={productUploading}
                      asChild
                    >
                      <span>
                        {productUploading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Upload className="h-4 w-4" />
                        )}
                        Add Product Image
                      </span>
                    </Button>
                  </div>
                </Label>
                <Input
                  id="product-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleProductImageUpload}
                  className="hidden"
                  disabled={productUploading}
                />
              </div>
            </div>
          )}

          {localProductImages.length === 0 && !uploadingImage && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-600">No product images uploaded</p>
              <p className="text-xs text-gray-500">Upload up to 10 product images</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default BusinessImageManager;

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import AvatarUpload from './AvatarUpload';
import { cloudflareService } from '@/services/cloudflareService';
import LocationAccordion from '@/components/LocationAccordion';
import { LocationService } from '@/services/locationService';

interface ProfileFormProps {
  id: string;
  initialFirstName: string;
  initialLastName: string;
  initialJobTitle: string;
  initialSustainabilityProfessional: boolean;
  initialAvatarUrl?: string | null;
  initialAvatarCloudflareKey?: string | null;
  initialBio?: string | null;
  initialOrganisationName?: string | null;
  initialLinkedinUrl?: string | null;
  initialTwitterUrl?: string | null;
  initialInstagramUrl?: string | null;
  initialTiktokUrl?: string | null;
  initialLocation?: string | null;
  onProfileUpdated: () => void;
}

const ProfileForm = ({
  id,
  initialFirstName,
  initialLastName,
  initialJobTitle,
  initialSustainabilityProfessional,
  initialAvatarUrl,
  initialAvatarCloudflareKey,
  initialBio,
  initialOrganisationName,
  initialLinkedinUrl,
  initialTwitterUrl,
  initialInstagramUrl,
  initialTiktokUrl,
  initialLocation,
  onProfileUpdated
}: ProfileFormProps) => {
  const [firstName, setFirstName] = useState(initialFirstName);
  const [lastName, setLastName] = useState(initialLastName);
  const [jobTitle, setJobTitle] = useState(initialJobTitle);
  const [sustainabilityProfessional, setSustainabilityProfessional] = useState(initialSustainabilityProfessional);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [avatarCloudflareKey, setAvatarCloudflareKey] = useState<string | null>(initialAvatarCloudflareKey || null);
  const [bio, setBio] = useState(initialBio || '');
  const [organisationName, setOrganisationName] = useState(initialOrganisationName || '');
  const [linkedinUrl, setLinkedinUrl] = useState(initialLinkedinUrl || '');
  const [twitterUrl, setTwitterUrl] = useState(initialTwitterUrl || '');
  const [instagramUrl, setInstagramUrl] = useState(initialInstagramUrl || '');
  const [tiktokUrl, setTiktokUrl] = useState(initialTiktokUrl || '');
  const [locationSelectionKey, setLocationSelectionKey] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  // Convert location_id to selection key on component mount
  useEffect(() => {
    const loadLocationSelectionKey = async () => {
      if (initialLocation) {
        try {
          const selectionKeys = await LocationService.convertIdsToSelectionKeys([initialLocation]);
          if (selectionKeys.length > 0) {
            setLocationSelectionKey(selectionKeys[0]);
          }
        } catch (error) {
          console.error('Failed to convert location ID to selection key:', error);
        }
      }
    };

    loadLocationSelectionKey();
  }, [initialLocation]);

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    try {
      // If we're removing an avatar, delete the old one from Cloudflare
      if (initialAvatarCloudflareKey && !avatarCloudflareKey) {
        await cloudflareService.deleteAvatar(initialAvatarCloudflareKey);
      }
      // Convert location selection key to UUID
      let locationId = null;
      if (locationSelectionKey) {
        const ids = await LocationService.convertSelectionKeysToIds([locationSelectionKey]);
        locationId = ids[0] || null;
      }
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName.trim() || null,
          last_name: lastName.trim() || null,
          job_title: jobTitle.trim() || null,
          sustainability_professional: sustainabilityProfessional,
          avatar_url: avatarUrl,
          avatar_cloudflare_key: avatarCloudflareKey,
          bio: bio.trim() || null,
          organisation_name: organisationName.trim() || null,
          linkedin_url: linkedinUrl.trim() || null,
          twitter_url: twitterUrl.trim() || null,
          instagram_url: instagramUrl.trim() || null,
          tiktok_url: tiktokUrl.trim() || null,
          location_id: locationId,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        toast({
          title: "Error",
          description: "Failed to update profile",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Success",
          description: "Profile updated successfully"
        });
        onProfileUpdated();
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAvatarUpdate = (url: string | null, key: string | null) => {
    setAvatarUrl(url);
    setAvatarCloudflareKey(key);
  };

  return (
    <form onSubmit={handleSave} className="space-y-6">
      {/* Avatar Upload Section */}
      <AvatarUpload
        currentAvatarUrl={avatarUrl}
        currentAvatarCloudflareKey={avatarCloudflareKey}
        onAvatarUpdate={handleAvatarUpdate}
        userId={id}
        firstName={firstName}
        lastName={lastName}
      />

      {/* Existing Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            placeholder="Enter your first name"
          />
        </div>
        
        <div>
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            placeholder="Enter your last name"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="jobTitle">Job Title</Label>
        <Input
          id="jobTitle"
          value={jobTitle}
          onChange={(e) => setJobTitle(e.target.value)}
          placeholder="Enter your job title"
        />
      </div>

      <div>
        <Label htmlFor="organisationName">Organisation</Label>
        <Input
          id="organisationName"
          value={organisationName}
          onChange={(e) => setOrganisationName(e.target.value)}
          placeholder="Enter your organisation name"
        />
      </div>

      <div>
        <Label htmlFor="bio">Bio</Label>
        <Textarea
          id="bio"
          value={bio}
          onChange={(e) => setBio(e.target.value)}
          placeholder="Tell us about yourself..."
          rows={3}
        />
      </div>

      {/* Social Media Handles */}
      <div className="space-y-4">
        <Label className="text-base font-semibold">Social Media Profiles</Label>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
            <Input
              id="linkedinUrl"
              value={linkedinUrl}
              onChange={(e) => setLinkedinUrl(e.target.value)}
              placeholder="https://linkedin.com/in/username"
              type="url"
            />
          </div>
          
          <div>
            <Label htmlFor="twitterUrl">Twitter/X URL</Label>
            <Input
              id="twitterUrl"
              value={twitterUrl}
              onChange={(e) => setTwitterUrl(e.target.value)}
              placeholder="https://twitter.com/username"
              type="url"
            />
          </div>
          
          <div>
            <Label htmlFor="instagramUrl">Instagram URL</Label>
            <Input
              id="instagramUrl"
              value={instagramUrl}
              onChange={(e) => setInstagramUrl(e.target.value)}
              placeholder="https://instagram.com/username"
              type="url"
            />
          </div>
          
          <div>
            <Label htmlFor="tiktokUrl">TikTok URL</Label>
            <Input
              id="tiktokUrl"
              value={tiktokUrl}
              onChange={(e) => setTiktokUrl(e.target.value)}
              placeholder="https://tiktok.com/@username"
              type="url"
            />
          </div>
        </div>
      </div>

      <div className="mb-6">
        <Label className="block mb-2 text-base font-semibold">Location</Label>
        <LocationAccordion
          allowMultiple={false}
          selectedItems={locationSelectionKey ? new Set([locationSelectionKey]) : new Set()}
          onSelectionChange={(set) => setLocationSelectionKey(Array.from(set)[0] || null)}
          title="Select Your Location"
          subtitle="Choose your main location (e.g. home or work base)"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="sustainability"
          checked={sustainabilityProfessional}
          onCheckedChange={(checked) => setSustainabilityProfessional(checked === true)}
        />
        <Label htmlFor="sustainability" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          I am a sustainability professional
        </Label>
      </div>

      <Button type="submit" disabled={saving} className="w-full">
        {saving ? 'Saving...' : 'Save Changes'}
      </Button>
    </form>
  );
};

export default ProfileForm;

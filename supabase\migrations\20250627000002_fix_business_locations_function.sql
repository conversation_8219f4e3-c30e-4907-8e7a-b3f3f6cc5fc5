-- Fix the ambiguous column reference in add_business_customer_locations function
CREATE OR REPLACE FUNCTION add_business_customer_locations(
    p_business_id UUID,
    p_location_ids UUID[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    loc_id UUID;
BEGIN
    -- Check if user owns the business
    IF NOT EXISTS (
        SELECT 1 FROM businesses 
        WHERE id = p_business_id AND owner_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You can only modify your own business locations';
    END IF;

    -- Insert new customer locations (ignore duplicates)
    FOREACH loc_id IN ARRAY p_location_ids
    LOOP
        INSERT INTO business_customer_locations (business_id, location_id)
        VALUES (p_business_id, loc_id)
        ON CONFLICT (business_id, location_id) DO NOTHING;
    END LOOP;
END;
$$;

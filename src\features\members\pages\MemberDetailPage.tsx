import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  MapPin, 
  Calendar, 
  Building2, 
  ArrowLeft, 
  ExternalLink, 
  Linkedin, 
  Instagram,
  Shield,
  User,
  Briefcase
} from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import AvatarDisplay from '@/features/profile/components/AvatarDisplay';
import type { Database } from '@/integrations/supabase/types';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import type { NetZeroSubcategoryWithCategory } from '@/types/netzero-categories.types';

type Profile = Database['public']['Tables']['profiles']['Row'];

// Location data structure
interface LocationInfo {
  id: string;
  name: string;
  slug: string;
  type: string;
  path: string;
}

export type MemberDetailItem = Profile & {
  location?: LocationInfo | null;
  netZeroInterests?: NetZeroSubcategoryWithCategory[];
};

// Custom X (Twitter) icon component
const XIcon = ({ className }: { className?: string }) => (
  <svg 
    viewBox="0 0 24 24" 
    className={className}
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const MemberDetailPage = () => {
  const { memberId } = useParams<{ memberId: string }>();
  const navigate = useNavigate();
  const [member, setMember] = useState<MemberDetailItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [primaryNetZeroCategory, setPrimaryNetZeroCategory] = useState<NetZeroSubcategoryWithCategory | null>(null);

  useEffect(() => {
    const fetchMemberDetails = async () => {
      if (!memberId) {
        setError('No member ID provided');
        setLoading(false);
        return;
      }

      try {
        // Fetch the member profile - using type assertion to avoid complex inference issues
        const { data: profileData, error: profileError } = await (supabase as any)
          .from('profiles')
          .select('*')
          .eq('id', memberId)
          .eq('profile_visible', true)
          .single();

        if (profileError) {
          console.error('Error fetching member profile:', profileError);
          setError('Member not found or not public');
          setLoading(false);
          return;
        }

        let memberWithLocation: MemberDetailItem = profileData as Profile;

        // Fetch location details if available
        if (profileData.location_id) {
          try {
            const { data: locationData, error: locationError } = await (supabase as any)
              .from('locations')
              .select('id, name, slug, type')
              .eq('id', profileData.location_id)
              .single();

            if (!locationError && locationData) {
              // Try to get full path using the get_location_path function
              try {
                const { data: pathData, error: pathError } = await (supabase as any)
                  .rpc('get_location_path', { location_id: profileData.location_id });
                
                memberWithLocation.location = {
                  id: locationData.id,
                  name: locationData.name,
                  slug: locationData.slug || '',
                  type: locationData.type || '',
                  path: (!pathError && pathData) ? pathData : locationData.name
                };
              } catch (pathErr) {
                // Fallback to just name if path function fails
                memberWithLocation.location = {
                  id: locationData.id,
                  name: locationData.name,
                  slug: locationData.slug || '',
                  type: locationData.type || '',
                  path: locationData.name
                };
              }
            }
          } catch (err) {
            console.error('Error fetching location:', err);
          }
        }

        // Fetch net-zero interests
        try {
          const { interests, primarySubcategoryId } = await NetZeroCategoryService.getUserInterests(memberId);
          memberWithLocation.netZeroInterests = interests;
          setPrimaryNetZeroCategory(interests.find(i => i.id === primarySubcategoryId) || null);
        } catch (err) {
          memberWithLocation.netZeroInterests = [];
          setPrimaryNetZeroCategory(null);
        }

        setMember(memberWithLocation);
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchMemberDetails();
  }, [memberId]);

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-48 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !member) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <Button variant="ghost" onClick={() => navigate('/members')} className="mb-6">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Members
        </Button>
        <Card>
          <CardContent className="text-center py-12">
            <h2 className="text-xl font-semibold mb-2">Member Not Found</h2>
            <p className="text-muted-foreground">{error || 'The requested member could not be found.'}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const memberName = `${member.first_name || ''} ${member.last_name || ''}`.trim() || 'Anonymous User';
  const hasGlobalSocials = member.linkedin_url || member.twitter_url || member.instagram_url || member.tiktok_url;

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Back Button */}
      <Button variant="ghost" onClick={() => navigate('/members')} className="mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back to Members
      </Button>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Left Column - Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Member Profile Card */}
          <Card>
            <CardHeader>
              <div className="flex items-start gap-6">
                <AvatarDisplay
                  avatarUrl={member.avatar_url}
                  firstName={member.first_name}
                  lastName={member.last_name}
                  email={`${member.first_name?.toLowerCase() || ''}${member.last_name?.toLowerCase() || ''}@example.com`}
                  size="xl"
                  shape="square"
                />
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <CardTitle className="text-2xl">{memberName}</CardTitle>
                    {member.sustainability_professional && (
                      <Badge className="bg-emerald-600 hover:bg-emerald-700 text-white">
                        <Shield className="w-3 h-3 mr-1" />
                        Sustainability Professional
                      </Badge>
                    )}
                  </div>
                  
                  {member.job_title && (
                    <div className="flex items-center gap-2 text-muted-foreground mb-2">
                      <Briefcase className="w-4 h-4" />
                      <span>{member.job_title}</span>
                    </div>
                  )}
                  
                  {member.organisation_name && (
                    <div className="flex items-center gap-2 text-muted-foreground mb-2">
                      <Building2 className="w-4 h-4" />
                      <span>{member.organisation_name}</span>
                    </div>
                  )}

                  {member.location && (
                    <div className="flex items-center gap-2 text-muted-foreground mb-2">
                      <MapPin className="w-4 h-4" />
                      <span>{member.location.name}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2 text-muted-foreground text-sm">
                    <Calendar className="w-4 h-4" />
                    <span>Member since {new Date(member.created_at || '').toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            {member.bio && (
              <CardContent>
                <Separator className="mb-4" />
                <h3 className="font-semibold mb-2">About</h3>
                <p className="text-muted-foreground leading-relaxed">{member.bio}</p>
              </CardContent>
            )}

            {member.netZeroInterests && member.netZeroInterests.length > 0 && (
              <CardContent>
                <Separator className="mb-4" />
                <h3 className="font-semibold text-lg mb-2">Net-Zero Interests</h3>
                <div className="flex flex-wrap gap-2 mb-2">
                  {member.netZeroInterests.map((subcategory) => {
                    const isPrimary = primaryNetZeroCategory?.id === subcategory.id;
                    return (
                      <Badge
                        key={subcategory.id}
                        variant={isPrimary ? "default" : "outline"}
                        className={isPrimary ? 'bg-green-600 hover:bg-green-700 text-white border-green-700 flex items-center gap-1' : ''}
                      >
                        {isPrimary && <Building2 className="w-4 h-4 mr-1" />}
                        {subcategory.name}
                      </Badge>
                    );
                  })}
                </div>
              </CardContent>
            )}
          </Card>
        </div>

        {/* Right Column - Social Media */}
        <div className="space-y-6">
          {/* Social Media Links */}
          {hasGlobalSocials && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Connect with {member.first_name || 'Member'}</CardTitle>
                <CardDescription>
                  Follow {member.first_name || 'this member'} on social media
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {member.linkedin_url && (
                  <a
                    href={member.linkedin_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-600 text-white">
                      <Linkedin className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">LinkedIn</p>
                      <p className="text-xs text-muted-foreground">Professional network</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}

                {member.twitter_url && (
                  <a
                    href={member.twitter_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-black text-white">
                      <XIcon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">X (Twitter)</p>
                      <p className="text-xs text-muted-foreground">Latest updates</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}

                {member.instagram_url && (
                  <a
                    href={member.instagram_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-purple-600 to-pink-600 text-white">
                      <Instagram className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">Instagram</p>
                      <p className="text-xs text-muted-foreground">Photos and stories</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}

                {member.tiktok_url && (
                  <a
                    href={member.tiktok_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground transition-colors"
                  >
                    <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-black text-white">
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-.88-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43V7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.43z"/>
                      </svg>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-sm">TikTok</p>
                      <p className="text-xs text-muted-foreground">Short videos</p>
                    </div>
                    <ExternalLink className="w-4 h-4 text-muted-foreground" />
                  </a>
                )}
              </CardContent>
            </Card>
          )}

          {!hasGlobalSocials && (
            <Card>
              <CardContent className="text-center py-8">
                <User className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-semibold mb-2">No Social Media Links</h3>
                <p className="text-sm text-muted-foreground">
                  {member.first_name || 'This member'} hasn't shared any social media profiles yet.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default MemberDetailPage;

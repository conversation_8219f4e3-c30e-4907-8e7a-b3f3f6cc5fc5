-- Seed data for development environment
-- Temporarily disable foreign key checks for seeding

-- Disable foreign key constraint checks
SET session_replication_role = replica;

-- Insert sample businesses directly
INSERT INTO public.businesses (
  id, owner_id, business_name, contact_email, contact_phone, 
  website, linkedin, twitter, address_line_1, address_line_2, 
  city, postcode
)
VALUES
  (
    '10000000-0000-0000-0000-000000000001',
    '00000000-0000-0000-0000-000000000001',
    'EcoTech Solutions Ltd',
    '<EMAIL>',
    '0121 123 4567',
    'https://www.ecotechltd.com',
    'https://linkedin.com/company/ecotech-solutions',
    '@EcoTechUK',
    '123 Green Street',
    'Innovation Hub',
    'Birmingham',
    'B1 1AA'
  ),
  (
    '10000000-0000-0000-0000-000000000002',
    '00000000-0000-0000-0000-000000000002',
    'SolarCo Renewables',
    '<EMAIL>',
    '0161 234 5678',
    'https://www.solarco.net',
    'https://linkedin.com/company/solarco',
    '@SolarCoUK',
    '456 Solar Avenue',
    'Tech Park',
    'Manchester',
    'M1 2BB'
  ),
  (
    '10000000-0000-0000-0000-000000000003',
    '00000000-0000-0000-0000-000000000003',
    'WindPower Dynamics',
    '<EMAIL>',
    '0131 345 6789',
    'https://www.windpower.co.uk',
    'https://linkedin.com/company/windpower-dynamics',
    '@WindPowerUK',
    '789 Turbine Lane',
    'Energy Quarter',
    'Edinburgh',
    'EH1 3CC'
  );

-- Re-enable foreign key constraint checks
SET session_replication_role = DEFAULT;

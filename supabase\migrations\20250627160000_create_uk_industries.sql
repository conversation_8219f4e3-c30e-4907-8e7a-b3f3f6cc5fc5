-- Create UK Industries System
-- This migration creates tables for UK industries with hierarchical structure
-- and junction tables for user and business industry associations

-- Create the main industries table with hierarchical structure
CREATE TABLE IF NOT EXISTS public.uk_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_id UUID REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    name VA<PERSON>HAR(255) NOT NULL,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(parent_id, name) -- Ensure unique names within the same parent level
);

-- Create junction table for user profile industry (single select - user's current industry)
CREATE TABLE IF NOT EXISTS public.profile_industry (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(profile_id) -- Ensure one industry per profile
);

-- Create junction table for business industry (single select - business's main industry)
CREATE TABLE IF NOT EXISTS public.business_industry (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id) -- Ensure one main industry per business
);

-- Create junction table for business target industries (multi-select - industries the business serves)
CREATE TABLE IF NOT EXISTS public.business_target_industries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    business_id UUID NOT NULL REFERENCES public.businesses(id) ON DELETE CASCADE,
    industry_id UUID NOT NULL REFERENCES public.uk_industries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, industry_id) -- Prevent duplicate target industries
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_uk_industries_parent_id ON public.uk_industries(parent_id);
CREATE INDEX IF NOT EXISTS idx_uk_industries_sort_order ON public.uk_industries(sort_order);
CREATE INDEX IF NOT EXISTS idx_profile_industry_profile_id ON public.profile_industry(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_industry_industry_id ON public.profile_industry(industry_id);
CREATE INDEX IF NOT EXISTS idx_business_industry_business_id ON public.business_industry(business_id);
CREATE INDEX IF NOT EXISTS idx_business_industry_industry_id ON public.business_industry(industry_id);
CREATE INDEX IF NOT EXISTS idx_business_target_industries_business_id ON public.business_target_industries(business_id);
CREATE INDEX IF NOT EXISTS idx_business_target_industries_industry_id ON public.business_target_industries(industry_id);

-- Add updated_at triggers
CREATE OR REPLACE TRIGGER handle_uk_industries_updated_at 
    BEFORE UPDATE ON public.uk_industries 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER handle_profile_industry_updated_at 
    BEFORE UPDATE ON public.profile_industry 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER handle_business_industry_updated_at 
    BEFORE UPDATE ON public.business_industry 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE OR REPLACE TRIGGER handle_business_target_industries_updated_at 
    BEFORE UPDATE ON public.business_target_industries 
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Grant permissions
GRANT ALL ON TABLE public.uk_industries TO anon;
GRANT ALL ON TABLE public.uk_industries TO authenticated;
GRANT ALL ON TABLE public.uk_industries TO service_role;

GRANT ALL ON TABLE public.profile_industry TO anon;
GRANT ALL ON TABLE public.profile_industry TO authenticated;
GRANT ALL ON TABLE public.profile_industry TO service_role;

GRANT ALL ON TABLE public.business_industry TO anon;
GRANT ALL ON TABLE public.business_industry TO authenticated;
GRANT ALL ON TABLE public.business_industry TO service_role;

GRANT ALL ON TABLE public.business_target_industries TO anon;
GRANT ALL ON TABLE public.business_target_industries TO authenticated;
GRANT ALL ON TABLE public.business_target_industries TO service_role;

-- Add comments for documentation
COMMENT ON TABLE public.uk_industries IS 'Hierarchical structure of UK industries (parent and child industries)';
COMMENT ON TABLE public.profile_industry IS 'User industry association (single select - user works in this industry)';
COMMENT ON TABLE public.business_industry IS 'Business main industry (single select - business operates in this industry)';
COMMENT ON TABLE public.business_target_industries IS 'Business target industries (multi-select - industries the business serves/targets)';

COMMENT ON COLUMN public.uk_industries.parent_id IS 'Reference to parent industry for hierarchical structure';
COMMENT ON COLUMN public.uk_industries.sort_order IS 'Order for displaying industries within the same parent level';
COMMENT ON COLUMN public.profile_industry.profile_id IS 'User profile reference';
COMMENT ON COLUMN public.profile_industry.industry_id IS 'Industry the user works in';
COMMENT ON COLUMN public.business_industry.business_id IS 'Business reference';
COMMENT ON COLUMN public.business_industry.industry_id IS 'Main industry the business operates in';
COMMENT ON COLUMN public.business_target_industries.business_id IS 'Business reference';
COMMENT ON COLUMN public.business_target_industries.industry_id IS 'Industry the business serves/targets';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          created_at: string | null
          id: string
          job_title: string | null
          first_name: string | null
          last_name: string | null
          sustainability_professional: boolean | null
          updated_at: string | null
          avatar_url: string | null
          avatar_cloudflare_key: string | null
          profile_visible: boolean | null
          newsletter_subscribed: boolean | null
          show_business_menu: boolean | null
          bio: string | null
          organisation_name: string | null
          linkedin_url: string | null
          twitter_url: string | null
          instagram_url: string | null
          tiktok_url: string | null
          location_id: string | null
        },
        Insert: {
          created_at?: string | null
          id: string
          job_title?: string | null
          first_name?: string | null
          last_name?: string | null
          sustainability_professional?: boolean | null
          updated_at?: string | null
          avatar_url?: string | null
          avatar_cloudflare_key?: string | null
          profile_visible?: boolean | null
          newsletter_subscribed?: boolean | null
          show_business_menu?: boolean | null
          bio?: string | null
          organisation_name?: string | null
          linkedin_url?: string | null
          twitter_url?: string | null
          instagram_url?: string | null
          tiktok_url?: string | null
          location_id?: string | null
        },
        Update: {
          created_at?: string | null
          id?: string
          job_title?: string | null
          first_name?: string | null
          last_name?: string | null
          sustainability_professional?: boolean | null
          updated_at?: string | null
          avatar_url?: string | null
          avatar_cloudflare_key?: string | null
          profile_visible?: boolean | null
          newsletter_subscribed?: boolean | null
          show_business_menu?: boolean | null
          bio?: string | null
          organisation_name?: string | null
          linkedin_url?: string | null
          twitter_url?: string | null
          instagram_url?: string | null
          tiktok_url?: string | null
          location_id?: string | null
        },
        Relationships: [
          {
            foreignKeyName: "profiles_location_id_fkey"
            columns: ["location_id"]
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      },
      businesses: {
        Row: {
          id: string
          owner_id: string
          business_name: string
          contact_email: string
          contact_phone: string | null
          website: string | null
          linkedin: string | null
          twitter: string | null
          address_line_1: string | null
          address_line_2: string | null
          city: string | null
          postcode: string | null
          created_at: string
          updated_at: string
          logo_url: string | null
          logo_cloudflare_key: string | null
          product_images: Json | null
          headquarters_location_id: string | null
        },
        Insert: {
          id?: string
          owner_id: string
          business_name: string
          contact_email: string
          contact_phone?: string | null
          website?: string | null
          linkedin?: string | null
          twitter?: string | null
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          postcode?: string | null
          created_at?: string
          updated_at?: string
          logo_url?: string | null
          logo_cloudflare_key?: string | null
          product_images?: Json | null
          headquarters_location_id?: string | null
        },
        Update: {
          id?: string
          owner_id?: string
          business_name?: string
          contact_email?: string
          contact_phone?: string | null
          website?: string | null
          linkedin?: string | null
          twitter?: string | null
          address_line_1?: string | null
          address_line_2?: string | null
          city?: string | null
          postcode?: string | null
          created_at?: string
          updated_at?: string
          logo_url?: string | null
          logo_cloudflare_key?: string | null
          product_images?: Json | null
          headquarters_location_id?: string | null
        },
        Relationships: [
          {
            foreignKeyName: "businesses_owner_id_fkey"
            columns: ["owner_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "businesses_headquarters_location_id_fkey"
            columns: ["headquarters_location_id"]
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      },
      locations: {
        Row: {
          id: string
          name: string
          slug: string
          type: string
          parent_id: string | null
          sort_order: number
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          name: string
          slug: string
          type: string
          parent_id?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          name?: string
          slug?: string
          type?: string
          parent_id?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "locations_parent_id_fkey"
            columns: ["parent_id"]
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      },
      netzero_categories: {
        Row: {
          id: string
          name: string
          description: string | null
          sort_order: number
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          name: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          name?: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Relationships: []
      },
      netzero_subcategories: {
        Row: {
          id: string
          category_id: string
          name: string
          description: string | null
          sort_order: number
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          category_id: string
          name: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          category_id?: string
          name?: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "netzero_subcategories_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "netzero_categories"
            referencedColumns: ["id"]
          }
        ]
      },
      profile_netzero_interests: {
        Row: {
          id: string
          profile_id: string
          subcategory_id: string
          created_at: string
        },
        Insert: {
          id?: string
          profile_id: string
          subcategory_id: string
          created_at?: string
        },
        Update: {
          id?: string
          profile_id?: string
          subcategory_id?: string
          created_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "profile_netzero_interests_profile_id_fkey"
            columns: ["profile_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_netzero_interests_subcategory_id_fkey"
            columns: ["subcategory_id"]
            referencedRelation: "netzero_subcategories"
            referencedColumns: ["id"]
          }
        ]
      },
      business_netzero_categories: {
        Row: {
          id: string
          business_id: string
          subcategory_id: string
          is_primary: boolean
          created_at: string
        },
        Insert: {
          id?: string
          business_id: string
          subcategory_id: string
          is_primary?: boolean
          created_at?: string
        },
        Update: {
          id?: string
          business_id?: string
          subcategory_id?: string
          is_primary?: boolean
          created_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "business_netzero_categories_business_id_fkey"
            columns: ["business_id"]
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_netzero_categories_subcategory_id_fkey"
            columns: ["subcategory_id"]
            referencedRelation: "netzero_subcategories"
            referencedColumns: ["id"]
          }
        ]
      },
      business_customer_locations: {
        Row: {
          id: string
          business_id: string
          location_id: string
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          business_id: string
          location_id: string
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          business_id?: string
          location_id?: string
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "business_customer_locations_business_id_fkey"
            columns: ["business_id"]
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_customer_locations_location_id_fkey"
            columns: ["location_id"]
            referencedRelation: "locations"
            referencedColumns: ["id"]
          }
        ]
      },
      uk_industries: {
        Row: {
          id: string
          parent_id: string | null
          name: string
          description: string | null
          sort_order: number
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          parent_id?: string | null
          name: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          parent_id?: string | null
          name?: string
          description?: string | null
          sort_order?: number
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "uk_industries_parent_id_fkey"
            columns: ["parent_id"]
            referencedRelation: "uk_industries"
            referencedColumns: ["id"]
          }
        ]
      },
      profile_industry: {
        Row: {
          id: string
          profile_id: string
          industry_id: string
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          profile_id: string
          industry_id: string
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          profile_id?: string
          industry_id?: string
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "profile_industry_profile_id_fkey"
            columns: ["profile_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_industry_industry_id_fkey"
            columns: ["industry_id"]
            referencedRelation: "uk_industries"
            referencedColumns: ["id"]
          }
        ]
      },
      business_industry: {
        Row: {
          id: string
          business_id: string
          industry_id: string
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          business_id: string
          industry_id: string
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          business_id?: string
          industry_id?: string
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "business_industry_business_id_fkey"
            columns: ["business_id"]
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_industry_industry_id_fkey"
            columns: ["industry_id"]
            referencedRelation: "uk_industries"
            referencedColumns: ["id"]
          }
        ]
      },
      business_target_industries: {
        Row: {
          id: string
          business_id: string
          industry_id: string
          created_at: string
          updated_at: string
        },
        Insert: {
          id?: string
          business_id: string
          industry_id: string
          created_at?: string
          updated_at?: string
        },
        Update: {
          id?: string
          business_id?: string
          industry_id?: string
          created_at?: string
          updated_at?: string
        },
        Relationships: [
          {
            foreignKeyName: "business_target_industries_business_id_fkey"
            columns: ["business_id"]
            referencedRelation: "businesses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "business_target_industries_industry_id_fkey"
            columns: ["industry_id"]
            referencedRelation: "uk_industries"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

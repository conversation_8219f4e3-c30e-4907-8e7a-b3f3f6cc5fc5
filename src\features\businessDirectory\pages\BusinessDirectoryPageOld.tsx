import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MapPin, Globe, Phone, Mail, Calendar, Building2, Search, X } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import BusinessLogoDisplay from '../components/BusinessLogoDisplay';
import BusinessSearchAndFilter from '../components/BusinessSearchAndFilter';
import { useBusinessSearchFilters } from '../hooks/useBusinessSearchFilters';
import type { Database } from '@/types/database.types';

type Business = Database['public']['Tables']['businesses']['Row'];

const BusinessDirectoryPage = () => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Use the search and filter hook
  const {
    searchTerm,
    setSearchTerm,
    locationFilter,
    setLocationFilter,
    sortBy,
    setSortBy,
    showFilters,
    setShowFilters,
    clearFilters,
    hasActiveFilters,
    locationOptions,
    filteredAndSortedBusinesses,
  } = useBusinessSearchFilters(businesses);

  useEffect(() => {
    const fetchBusinesses = async () => {
      try {
        const { data, error } = await supabase
          .from('businesses')
          .select('*')
          .order('business_name');

        if (error) throw error;
        
        setBusinesses((data || []) as Business[]);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while fetching businesses');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinesses();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Business Directory</h1>
        <p className="text-muted-foreground">
          Browse sustainable businesses in our network.
        </p>
      </div>

      {/* Search and Filter Section */}
      <BusinessSearchAndFilter
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        locationFilter={locationFilter}
        onLocationFilterChange={setLocationFilter}
        sortBy={sortBy}
        onSortChange={setSortBy}
        showFilters={showFilters}
        onToggleFilters={() => setShowFilters(!showFilters)}
        onClearFilters={clearFilters}
        hasActiveFilters={hasActiveFilters}
        locationOptions={locationOptions}
        resultCount={filteredAndSortedBusinesses.length}
        totalCount={businesses.length}
      />
      
      {loading && (
        <div className="text-center py-8">
          <p>Loading businesses...</p>
        </div>
      )}

      {error && (
        <div className="p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg">
          <p>{error}</p>
        </div>
      )}

      {!loading && !error && businesses.length === 0 && (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <p className="text-lg">No Businesses Found</p>
          <p className="text-muted-foreground mt-2">
            Be the first to add your sustainable business to our directory.
          </p>
        </div>
      )}

      {!loading && !error && businesses.length > 0 && filteredAndSortedBusinesses.length === 0 && (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg">No businesses match your search criteria</p>
          <p className="text-muted-foreground mt-2 mb-4">
            Try adjusting your search terms or clearing the filters.
          </p>
          <Button onClick={clearFilters} variant="outline">
            <X className="w-4 h-4 mr-2" />
            Clear Filters
          </Button>
        </div>
      )}

      {!loading && !error && filteredAndSortedBusinesses.length > 0 && (
        <>
          {/* Results Summary */}
          <div className="mb-4 flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredAndSortedBusinesses.length} of {businesses.length} businesses
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredAndSortedBusinesses.map((business) => (
            <Card 
              key={business.id} 
              className="cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => navigate(`/business/${business.id}`)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start gap-3">
                  <BusinessLogoDisplay
                    logoUrl={business.logo_url}
                    businessName={business.business_name}
                    size="lg"
                    shape="square"
                  />
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg leading-tight">
                      {business.business_name}
                    </CardTitle>
                    {business.contact_email && (
                      <p className="text-sm text-muted-foreground mt-1 leading-tight">
                        {business.contact_email}
                      </p>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Contact Information */}
                  <div className="space-y-2">
                    {business.website && (
                      <div className="flex items-center gap-2 text-sm">
                        <Globe className="w-4 h-4 text-muted-foreground" />
                        <a 
                          href={business.website} 
                          target="_blank" 
                          rel="noopener noreferrer" 
                          className="text-primary hover:underline truncate"
                          onClick={(e) => e.stopPropagation()}
                        >
                          {business.website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    )}
                    
                    {business.contact_phone && (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="w-4 h-4 text-muted-foreground" />
                        <span>{business.contact_phone}</span>
                      </div>
                    )}
                    
                    {(business.city || business.postcode) && (
                      <div className="flex items-center gap-2 text-sm">
                        <MapPin className="w-4 h-4 text-muted-foreground" />
                        <span>
                          {[business.city, business.postcode].filter(Boolean).join(', ')}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-1 text-muted-foreground text-xs mt-3 pt-3 border-t">
                    <Calendar className="w-3 h-3" />
                    Added {new Date(business.created_at || '').toLocaleDateString()}
                  </div>
                </div>
              </CardContent>
            </Card>            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default BusinessDirectoryPage;

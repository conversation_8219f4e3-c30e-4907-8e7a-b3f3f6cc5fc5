-- Add headquarters location to businesses table
ALTER TABLE public.businesses 
ADD COLUMN headquarters_location_id UUID REFERENCES locations(id) ON DELETE SET NULL;

-- Create junction table for business customer locations
CREATE TABLE public.business_customer_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    business_id UUID NOT NULL REFERENCES businesses(id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES locations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(business_id, location_id)
);

-- Add indexes for better performance
CREATE INDEX idx_business_customer_locations_business_id ON business_customer_locations(business_id);
CREATE INDEX idx_business_customer_locations_location_id ON business_customer_locations(location_id);
CREATE INDEX idx_businesses_headquarters_location_id ON businesses(headquarters_location_id);

-- <PERSON>reate trigger to automatically update updated_at for business_customer_locations
CREATE TRIGGER update_business_customer_locations_updated_at 
    BEFORE UPDATE ON business_customer_locations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies for business_customer_locations
ALTER TABLE business_customer_locations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view customer locations for any business (for public directory)
CREATE POLICY "Anyone can view business customer locations" ON business_customer_locations
    FOR SELECT USING (true);

-- Policy: Business owners can manage their own customer locations
CREATE POLICY "Business owners can manage their customer locations" ON business_customer_locations
    FOR ALL USING (
        business_id IN (
            SELECT id FROM businesses WHERE owner_id = auth.uid()
        )
    );

-- Create a view to get business locations with full location details
CREATE OR REPLACE VIEW business_locations_view AS
SELECT 
    b.id as business_id,
    b.business_name,
    hq.id as headquarters_id,
    hq.name as headquarters_name,
    hq.slug as headquarters_slug,
    get_location_path(hq.id) as headquarters_path,
    array_agg(
        json_build_object(
            'id', cl.id,
            'name', cl.name,
            'slug', cl.slug,
            'type', cl.type,
            'path', get_location_path(cl.id)
        )
    ) FILTER (WHERE cl.id IS NOT NULL) as customer_locations
FROM businesses b
LEFT JOIN locations hq ON b.headquarters_location_id = hq.id
LEFT JOIN business_customer_locations bcl ON b.id = bcl.business_id
LEFT JOIN locations cl ON bcl.location_id = cl.id
GROUP BY b.id, b.business_name, hq.id, hq.name, hq.slug;

-- Grant permissions
GRANT SELECT ON business_locations_view TO authenticated, anon;

-- Function to add customer locations for a business
CREATE OR REPLACE FUNCTION add_business_customer_locations(
    p_business_id UUID,
    p_location_ids UUID[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    loc_id UUID;
BEGIN
    -- Check if user owns the business
    IF NOT EXISTS (
        SELECT 1 FROM businesses 
        WHERE id = p_business_id AND owner_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You can only modify your own business locations';
    END IF;

    -- Insert new customer locations (ignore duplicates)
    FOREACH loc_id IN ARRAY p_location_ids
    LOOP
        INSERT INTO business_customer_locations (business_id, location_id)
        VALUES (p_business_id, loc_id)
        ON CONFLICT (business_id, location_id) DO NOTHING;
    END LOOP;
END;
$$;

-- Function to remove customer locations for a business
CREATE OR REPLACE FUNCTION remove_business_customer_locations(
    p_business_id UUID,
    p_location_ids UUID[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user owns the business
    IF NOT EXISTS (
        SELECT 1 FROM businesses 
        WHERE id = p_business_id AND owner_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You can only modify your own business locations';
    END IF;

    -- Remove customer locations
    DELETE FROM business_customer_locations
    WHERE business_id = p_business_id 
    AND location_id = ANY(p_location_ids);
END;
$$;

-- Function to set all customer locations for a business (replaces existing)
CREATE OR REPLACE FUNCTION set_business_customer_locations(
    p_business_id UUID,
    p_location_ids UUID[]
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user owns the business
    IF NOT EXISTS (
        SELECT 1 FROM businesses 
        WHERE id = p_business_id AND owner_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You can only modify your own business locations';
    END IF;

    -- Remove all existing customer locations for this business
    DELETE FROM business_customer_locations WHERE business_id = p_business_id;

    -- Add new customer locations
    IF p_location_ids IS NOT NULL AND array_length(p_location_ids, 1) > 0 THEN
        PERFORM add_business_customer_locations(p_business_id, p_location_ids);
    END IF;
END;
$$;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION add_business_customer_locations TO authenticated;
GRANT EXECUTE ON FUNCTION remove_business_customer_locations TO authenticated;
GRANT EXECUTE ON FUNCTION set_business_customer_locations TO authenticated;

import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

const Dashboard = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Dashboard</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-primary/10 p-4 rounded-md border border-primary/20">
            <h3 className="font-medium mb-2 text-foreground">Activity</h3>
            <p className="text-muted-foreground text-sm">
              Your recent activity will appear here.
            </p>
          </div>
          
          <div className="bg-secondary p-4 rounded-md border border-secondary/50">
            <h3 className="font-medium mb-2 text-secondary-foreground">Progress</h3>
            <p className="text-secondary-foreground/80 text-sm">
              Track your sustainability progress here.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Dashboard;

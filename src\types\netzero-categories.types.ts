// Net-Zero Categories types for the Net Zero Nexus Hub
import { Database } from '@/integrations/supabase/types';

// Database table types
export type NetZeroCategory = Database['public']['Tables']['netzero_categories']['Row'];
export type NetZeroSubcategory = Database['public']['Tables']['netzero_subcategories']['Row'];
export type ProfileNetZeroInterest = Database['public']['Tables']['profile_netzero_interests']['Row'];
export type BusinessNetZeroCategory = Database['public']['Tables']['business_netzero_categories']['Row'];

// Insert types for creating new records
export type NewNetZeroCategory = Database['public']['Tables']['netzero_categories']['Insert'];
export type NewNetZeroSubcategory = Database['public']['Tables']['netzero_subcategories']['Insert'];
export type NewProfileNetZeroInterest = Database['public']['Tables']['profile_netzero_interests']['Insert'];
export type NewBusinessNetZeroCategory = Database['public']['Tables']['business_netzero_categories']['Insert'];

// Extended types with relationships
export interface NetZeroCategoryWithSubcategories extends NetZeroCategory {
  subcategories: NetZeroSubcategory[];
}

export interface NetZeroSubcategoryWithCategory extends NetZeroSubcategory {
  category: NetZeroCategory;
}

export interface ProfileWithNetZeroInterests {
  id: string;
  first_name: string | null;
  last_name: string | null;
  interests: NetZeroSubcategoryWithCategory[];
}

export interface BusinessWithNetZeroCategories {
  id: string;
  business_name: string;
  categories: NetZeroSubcategoryWithCategory[];
  primary_category?: NetZeroSubcategoryWithCategory;
}

// Form data types
export interface NetZeroCategorySelection {
  subcategoryId: string;
  categoryId: string;
  categoryName: string;
  subcategoryName: string;
  isPrimary?: boolean;
}

export interface ProfileNetZeroInterestsFormData {
  selectedSubcategories: string[]; // Array of subcategory IDs
  primarySubcategoryId?: string; // Primary category for profile
}

export interface BusinessNetZeroCategoriesFormData {
  selectedSubcategories: string[]; // Array of subcategory IDs
  primarySubcategoryId?: string; // Primary category for business
}

// Hierarchical structure for UI components
export interface NetZeroCategoryHierarchy {
  [categoryId: string]: {
    id: string;
    name: string;
    description: string | null;
    sort_order: number;
    subcategories: {
      [subcategoryId: string]: {
        id: string;
        name: string;
        description: string | null;
        sort_order: number;
      };
    };
  };
}

// Selection state for multi-select components
export interface CategorySelectionState {
  selectedSubcategories: Set<string>;
  expandedCategories: Set<string>;
}

// API response types
export interface NetZeroCategoriesResponse {
  categories: NetZeroCategoryWithSubcategories[];
}

export interface UserInterestsResponse {
  interests: NetZeroSubcategoryWithCategory[];
  primarySubcategoryId?: string;
}

export interface BusinessCategoriesResponse {
  categories: NetZeroSubcategoryWithCategory[];
  primary_category?: NetZeroSubcategoryWithCategory;
}

// Constants for the 7 main categories
export const NET_ZERO_CATEGORY_NAMES = {
  ENERGY: 'Energy',
  TRANSPORTATION: 'Transportation',
  BUILDINGS_FACILITIES: 'Buildings and Facilities',
  MANUFACTURING_OPERATIONS: 'Manufacturing and Operations',
  AGRICULTURE_LAND_USE: 'Agriculture and Land Use',
  CARBON_MANAGEMENT: 'Carbon Management',
  STRATEGY_FINANCE_GOVERNANCE: 'Strategy, Finance and Governance'
} as const;

export type NetZeroCategoryName = typeof NET_ZERO_CATEGORY_NAMES[keyof typeof NET_ZERO_CATEGORY_NAMES];

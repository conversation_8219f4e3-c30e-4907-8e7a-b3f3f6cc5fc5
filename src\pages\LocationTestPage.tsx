import { useState } from 'react';
import { 
  LocationAccordion, 
  BusinessLocationForm, 
  BusinessDirectoryWithLocationFilter 
} from '../features/businessDirectory/components';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';

const LocationTestPage = () => {
  const [selectedLocations, setSelectedLocations] = useState<Set<string>>(new Set());
  
  // Mock business ID for testing
  const mockBusinessId = "00000000-0000-0000-0000-000000000000";

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Location Management System
          </h1>
          <p className="text-lg text-gray-600">
            Test the location selection and business location management components
          </p>
        </div>

        <Tabs defaultValue="accordion" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="accordion">Location Accordion</TabsTrigger>
            <TabsTrigger value="business-form">Business Location Form</TabsTrigger>
            <TabsTrigger value="directory">Business Directory</TabsTrigger>
          </TabsList>

          <TabsContent value="accordion" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Location Selection Component</CardTitle>
              </CardHeader>
              <CardContent>
                <LocationAccordion
                  selectedItems={selectedLocations}
                  onSelectionChange={setSelectedLocations}
                  title="Select UK Locations"
                  subtitle="Choose regions where your business operates"
                  allowMultiple={true}
                />
              </CardContent>
            </Card>

            {/* Display selected locations */}
            {selectedLocations.size > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Selected Locations Debug</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="font-medium">Selected keys:</p>
                    <div className="bg-gray-100 p-4 rounded-md">
                      <pre className="text-sm">
                        {JSON.stringify(Array.from(selectedLocations), null, 2)}
                      </pre>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="business-form" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Location Management</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <p className="mb-4">
                    This would show the business location form, but requires a valid business ID.
                  </p>
                  <p className="text-sm">
                    In a real application, this would be populated with the current user's business data.
                  </p>
                </div>
                {/* Uncomment when you have a real business to test with:
                <BusinessLocationForm
                  businessId={mockBusinessId}
                  onSave={() => console.log('Saved!')}
                  onCancel={() => console.log('Cancelled!')}
                />
                */}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="directory" className="space-y-6">
            <BusinessDirectoryWithLocationFilter />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default LocationTestPage;

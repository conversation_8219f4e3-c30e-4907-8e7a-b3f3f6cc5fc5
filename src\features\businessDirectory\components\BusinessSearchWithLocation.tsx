import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, X, SortAsc, SortDesc, Calendar, MapPin } from "lucide-react";
import LocationAccordion from "@/components/LocationAccordion";

export type SortOption = 'name_asc' | 'name_desc' | 'date_asc' | 'date_desc';

interface BusinessSearchWithLocationProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  sortBy: SortOption;
  onSortChange: (value: SortOption) => void;
  showFilters: boolean;
  onToggleFilters: () => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  resultCount: number;
  totalCount: number;
  // Location-specific props
  selectedHQLocations: Set<string>;
  onHQLocationsChange: (locations: Set<string>) => void;
  selectedCustomerLocations: Set<string>;
  onCustomerLocationsChange: (locations: Set<string>) => void;
}

const BusinessSearchWithLocation: React.FC<BusinessSearchWithLocationProps> = ({
  searchTerm,
  onSearchChange,
  sortBy,
  onSortChange,
  showFilters,
  onToggleFilters,
  onClearFilters,
  hasActiveFilters,
  resultCount,
  totalCount,
  selectedHQLocations,
  onHQLocationsChange,
  selectedCustomerLocations,
  onCustomerLocationsChange,
}) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search & Filter Businesses
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {resultCount} of {totalCount} businesses
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleFilters}
            >
              <Filter className="w-4 h-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search businesses by name, email, or website..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="space-y-6 pt-4 border-t">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Headquarters Location Filter */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-green-600" />
                  <Label className="text-sm font-medium">Filter by Headquarters Location</Label>
                </div>
                <LocationAccordion
                  selectedItems={selectedHQLocations}
                  onSelectionChange={onHQLocationsChange}
                  title="Headquarters Location"
                  subtitle="Find businesses headquartered in these areas"
                  allowMultiple={true}
                />
              </div>

              {/* Customer Location Filter */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-blue-600" />
                  <Label className="text-sm font-medium">Filter by Customer Locations</Label>
                </div>
                <LocationAccordion
                  selectedItems={selectedCustomerLocations}
                  onSelectionChange={onCustomerLocationsChange}
                  title="Customer Regions"
                  subtitle="Find businesses that serve customers in these areas"
                  allowMultiple={true}
                />
              </div>
            </div>

            {/* Sort Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Label htmlFor="sort-by">Sort by</Label>
                <Select value={sortBy} onValueChange={onSortChange}>
                  <SelectTrigger id="sort-by">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name_asc">
                      <div className="flex items-center gap-2">
                        <SortAsc className="w-4 h-4" />
                        Name A-Z
                      </div>
                    </SelectItem>
                    <SelectItem value="name_desc">
                      <div className="flex items-center gap-2">
                        <SortDesc className="w-4 h-4" />
                        Name Z-A
                      </div>
                    </SelectItem>
                    <SelectItem value="date_desc">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Newest first
                      </div>
                    </SelectItem>
                    <SelectItem value="date_asc">
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Oldest first
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Clear Filters */}
              <div className="space-y-2">
                <Label>&nbsp;</Label>
                <Button 
                  variant="outline" 
                  onClick={onClearFilters}
                  disabled={!hasActiveFilters}
                  className="w-full"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            {searchTerm && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: "{searchTerm}"
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onSearchChange('')}
                />
              </Badge>
            )}
            {selectedHQLocations.size > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                HQ Locations: {selectedHQLocations.size} selected
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onHQLocationsChange(new Set())}
                />
              </Badge>
            )}
            {selectedCustomerLocations.size > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Customer Locations: {selectedCustomerLocations.size} selected
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onCustomerLocationsChange(new Set())}
                />
              </Badge>
            )}
            {sortBy !== 'name_asc' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Sort: {sortBy.replace('_', ' ')}
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onSortChange('name_asc')}
                />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BusinessSearchWithLocation;

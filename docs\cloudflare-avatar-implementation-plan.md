# Cloudflare Avatar Storage Implementation Plan

## Overview
This document outlines the implementation plan for adding Cloudflare R2 storage for user profile avatars, including proper cleanup when users delete their profiles.

## Current State Analysis

### Database Schema
- **profiles table** currently has:
  - `id` (uuid, primary key)
  - `first_name` (text)
  - `last_name` (text)
  - `job_title` (text)
  - `sustainability_professional` (boolean)
  - `created_at` (timestamp)
  - `updated_at` (timestamp)

### Missing Components
1. Avatar URL column in profiles table
2. Avatar upload functionality
3. Cloudflare R2 integration
4. Proper cleanup mechanism for orphaned images

## Implementation Plan

### Phase 1: Database Schema Updates

#### 1.1 Add Avatar Column
Create a new migration to add avatar-related columns:

```sql
-- Migration: add_avatar_support_to_profiles.sql
ALTER TABLE public.profiles 
ADD COLUMN avatar_url text,
ADD COLUMN avatar_cloudflare_key text; -- Store the Cloudflare key for deletion

-- Update the members_view to include avatar
DROP VIEW IF EXISTS public.members_view;
CREATE VIEW public.members_view AS
SELECT 
  id,
  first_name,
  last_name,
  job_title,
  sustainability_professional,
  avatar_url,
  created_at AS joinedat
FROM public.profiles;
```

**Why two columns?**
- `avatar_url`: Public URL for displaying the image
- `avatar_cloudflare_key`: Internal key/path needed for deletion from Cloudflare

### Phase 2: Cloudflare R2 Integration

#### 2.1 Environment Variables
Add to your environment configuration:
```bash
VITE_CLOUDFLARE_ACCOUNT_ID=your_account_id
VITE_CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
VITE_CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key
VITE_CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_key
VITE_CLOUDFLARE_R2_ENDPOINT=your_r2_endpoint
VITE_CLOUDFLARE_PUBLIC_URL=your_public_domain
```

#### 2.2 Cloudflare Service Implementation
Create a service to handle Cloudflare operations:

```typescript
// src/services/cloudflareService.ts
export class CloudflareService {
  async uploadAvatar(file: File, userId: string): Promise<{url: string, key: string}>
  async deleteAvatar(key: string): Promise<boolean>
  private generateAvatarKey(userId: string, filename: string): string
}
```

### Phase 3: Frontend Components

#### 3.1 Avatar Upload Component
Create a new component for avatar upload:
```typescript
// src/features/profile/components/AvatarUpload.tsx
interface AvatarUploadProps {
  currentAvatarUrl?: string;
  onAvatarUpdate: (url: string, key: string) => void;
  userId: string;
}
```

#### 3.2 Update ProfileForm Component
Extend the existing ProfileForm to include avatar functionality:
- Add avatar upload field
- Handle avatar update in form submission
- Show current avatar with option to change/remove

### Phase 4: Backend Integration

#### 4.1 Profile Update Logic
Update the profile update logic to handle avatar changes:
```typescript
// Update profile with avatar
const updateProfile = async (profileData: {
  // ...existing fields
  avatar_url?: string;
  avatar_cloudflare_key?: string;
}) => {
  // If there's an old avatar and we're updating/removing it
  if (oldAvatarKey && oldAvatarKey !== profileData.avatar_cloudflare_key) {
    await cloudflareService.deleteAvatar(oldAvatarKey);
  }
  
  // Update profile in database
  await supabase.from('profiles').update(profileData).eq('id', userId);
}
```

#### 4.2 Account Deletion Hook
Create a cleanup function for when users delete their accounts:

```typescript
// src/hooks/useAccountDeletion.ts
export const useAccountDeletion = () => {
  const cleanupUserAssets = async (userId: string) => {
    // Get user's avatar key
    const { data: profile } = await supabase
      .from('profiles')
      .select('avatar_cloudflare_key')
      .eq('id', userId)
      .single();
    
    // Delete avatar from Cloudflare if exists
    if (profile?.avatar_cloudflare_key) {
      await cloudflareService.deleteAvatar(profile.avatar_cloudflare_key);
    }
    
    // Delete profile (this should cascade delete the user)
    await supabase.from('profiles').delete().eq('id', userId);
  };
  
  return { cleanupUserAssets };
};
```

### Phase 5: Error Handling & Edge Cases

#### 5.1 Upload Failure Handling
- Handle network failures during upload
- Rollback database changes if Cloudflare upload fails
- Show appropriate error messages to users

#### 5.2 Deletion Failure Handling
- Log but don't fail profile updates if avatar deletion fails
- Implement a cleanup job to remove orphaned images
- Consider implementing a "soft delete" with cleanup later

#### 5.3 File Validation
- Validate file types (jpg, png, webp)
- Limit file size (e.g., 5MB max)
- Validate image dimensions

### Phase 6: Database Functions for Cleanup

#### 6.1 Profile Deletion Trigger
Create a database function that automatically cleans up avatars:

```sql
-- Function to handle avatar cleanup on profile deletion
CREATE OR REPLACE FUNCTION cleanup_profile_assets()
RETURNS TRIGGER AS $$
BEGIN
  -- Store the avatar key for external cleanup
  IF OLD.avatar_cloudflare_key IS NOT NULL THEN
    INSERT INTO avatar_cleanup_queue (cloudflare_key, deleted_at)
    VALUES (OLD.avatar_cloudflare_key, NOW());
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Trigger for profile deletion
CREATE TRIGGER profile_deletion_cleanup
  BEFORE DELETE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_profile_assets();
```

#### 6.2 Cleanup Queue Table
```sql
-- Table to queue avatar deletions
CREATE TABLE avatar_cleanup_queue (
  id SERIAL PRIMARY KEY,
  cloudflare_key TEXT NOT NULL,
  deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed BOOLEAN DEFAULT FALSE
);
```

## File Structure Changes

```
src/
├── services/
│   ├── cloudflareService.ts          # New: Cloudflare R2 operations
│   └── avatarService.ts              # New: Avatar-specific logic
├── features/profile/
│   ├── components/
│   │   ├── AvatarUpload.tsx          # New: Avatar upload component
│   │   ├── AvatarDisplay.tsx         # New: Avatar display component
│   │   └── ProfileForm.tsx           # Updated: Include avatar
│   └── hooks/
│       └── useAvatar.ts              # New: Avatar management hook
├── hooks/
│   └── useAccountDeletion.ts         # Updated: Include avatar cleanup
└── types/
    └── avatar.types.ts               # New: Avatar-related types
```

## Implementation Steps

### Step 1: Database Migration
1. Create and run the avatar columns migration
2. Update TypeScript types to include new fields

### Step 2: Cloudflare Service
1. Implement CloudflareService class
2. Add environment variables
3. Test upload/delete functionality

### Step 3: Avatar Components
1. Create AvatarUpload component
2. Create AvatarDisplay component
3. Update ProfileForm to include avatar

### Step 4: Integration
1. Update UserProfilePage to show avatar
2. Update profile fetching to include avatar fields
3. Test full upload/update/delete flow

### Step 5: Cleanup Implementation
1. Implement account deletion cleanup
2. Create cleanup queue system
3. Add error handling and logging

## Testing Strategy

### Unit Tests
- CloudflareService upload/delete methods
- Avatar validation functions
- Profile update logic with avatar handling

### Integration Tests
- Full avatar upload flow
- Profile deletion with avatar cleanup
- Error scenarios (network failures, invalid files)

### Manual Testing
- Upload different file types and sizes
- Test avatar display in various components
- Verify cleanup when deleting profiles

## Security Considerations

1. **File Type Validation**: Only allow safe image formats
2. **File Size Limits**: Prevent abuse with large files
3. **Access Controls**: Ensure users can only modify their own avatars
4. **Secure URLs**: Use signed URLs if needed for privacy
5. **Rate Limiting**: Prevent spam uploads

## Performance Considerations

1. **Image Optimization**: Consider resizing images before upload
2. **Caching**: Implement proper caching headers
3. **CDN**: Leverage Cloudflare's CDN for fast delivery
4. **Lazy Loading**: Load avatars only when needed

## Rollback Plan

If issues arise:
1. Disable avatar upload feature via feature flag
2. Keep existing avatars but prevent new uploads
3. Database rollback: Remove avatar columns if needed
4. Clean up any orphaned files in Cloudflare

## Future Enhancements

1. **Multiple Avatar Sizes**: Generate thumbnails automatically
2. **Avatar Cropping**: Allow users to crop uploaded images
3. **Default Avatars**: Generate default avatars based on initials
4. **Avatar History**: Keep track of previous avatars
5. **Bulk Cleanup**: Admin tool to clean up orphaned files

## Dependencies

- **@aws-sdk/client-s3**: For S3-compatible API calls to Cloudflare R2
- **react-dropzone**: For file upload UI (optional)
- **react-image-crop**: For image cropping (future enhancement)

## Estimated Timeline

- **Phase 1-2**: 2-3 days (Database + Cloudflare integration)
- **Phase 3**: 2-3 days (Frontend components)
- **Phase 4**: 1-2 days (Backend integration)
- **Phase 5-6**: 2-3 days (Error handling + cleanup)
- **Testing**: 1-2 days

**Total**: 8-13 days

## Notes

- The dual-column approach (URL + key) ensures we can always delete files even if the URL format changes
- The cleanup queue provides resilience against deletion failures
- Consider implementing this in stages to minimize risk
- Monitor Cloudflare usage and costs after implementation

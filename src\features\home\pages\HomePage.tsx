import { useAuth } from "@/contexts/AuthContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowRight, Leaf, Users, Target, TrendingUp } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

const HomePage = () => {
  const { user, loading } = useAuth();
  const [firstName, setFirstName] = useState<string | null>(null);
  
  // Fetch user's first name when they're logged in
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('first_name')
          .eq('id', user.id)
          .single();
          
        if (error) {
          console.error('Error fetching user profile:', error);
          return;
        }
        
        if (data && data.first_name) {
          setFirstName(data.first_name);
        }
      } catch (err) {
        console.error('Error in fetchUserProfile:', err);
      }
    };
    
    fetchUserProfile();
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  const features = [
    {
      icon: Leaf,
      title: "Carbon Tracking",
      description: "Monitor and track your carbon footprint with detailed analytics and insights."
    },
    {
      icon: Users,
      title: "Community Network",
      description: "Connect with like-minded individuals working towards net-zero goals."
    },
    {
      icon: Target,
      title: "Goal Setting",
      description: "Set and achieve personalized sustainability targets with our tools."
    },
    {
      icon: TrendingUp,
      title: "Progress Analytics",
      description: "Visualize your journey with comprehensive reports and trends."
    }
  ];

  return (
    <div className="container mx-auto px-4">
      {/* Hero Section */}
      <div className="py-16 text-center">
        <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
          Welcome to Net Zero Nexus
        </h1>
        <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
          {user 
            ? `Welcome back${firstName ? `, ${firstName}` : ''}! Continue your journey towards a sustainable future.` 
            : 'Join our community of changemakers and accelerate your journey to net-zero emissions. Together, we can build a sustainable future.'
          }
        </p>
        
        <div className="flex gap-4 justify-center flex-wrap">
          {user ? (
            <>
              <Link to="/profile">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  View Dashboard
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </Link>
              <Link to="/members">
                <Button size="lg" variant="outline" className="text-foreground border-border hover:bg-background hover:text-primary">
                  Explore Community
                </Button>
              </Link>
            </>
          ) : (
            <Link to="/auth">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                Get Started Today
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          )}
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-foreground mb-4">
            Powerful Tools for Sustainable Change
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Our platform provides everything you need to track, analyze, and reduce your environmental impact.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {features.map((feature, index) => (
            <Card key={index} className="bg-card/50 border-border hover:bg-card/70 transition-colors">
              <CardHeader className="text-center">
                <div className="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="w-6 h-6 text-primary-foreground" />
                </div>
                <CardTitle className="text-primary-foreground">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground text-center">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Call to Action Section */}
      {!user && (
        <div className="py-16 text-center">
          <div className="bg-card/30 rounded-2xl p-8 border border-border">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              Ready to Make a Difference?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-xl mx-auto">
              Join thousands of individuals and organizations already using Net Zero Nexus to achieve their sustainability goals.
            </p>
            <Link to="/auth">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                Start Your Journey
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default HomePage;

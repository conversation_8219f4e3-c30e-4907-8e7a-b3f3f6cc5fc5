-- Remove cleanup queue tables and use direct deletion like avatars
-- This migration removes the cleanup tables and related triggers

-- Drop business image cleanup components
DROP TRIGGER IF EXISTS business_deletion_cleanup ON public.businesses;
DROP FUNCTION IF EXISTS public.cleanup_business_assets();
DROP TABLE IF EXISTS public.business_image_cleanup_queue;

-- Drop avatar cleanup components  
DROP TRIGGER IF EXISTS profile_deletion_cleanup ON public.profiles;
DROP FUNCTION IF EXISTS public.cleanup_profile_assets();
DROP TABLE IF EXISTS public.avatar_cleanup_queue;

-- Add comment explaining the new approach
COMMENT ON COLUMN public.businesses.logo_cloudflare_key IS 'Cloudflare R2 key for logo deletion - handled directly in application';
COMMENT ON COLUMN public.profiles.avatar_cloudflare_key IS 'Cloudflare R2 key for avatar deletion - handled directly in application';

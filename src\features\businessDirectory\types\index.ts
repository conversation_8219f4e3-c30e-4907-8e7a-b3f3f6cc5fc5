import { Database } from '@/types/database.types'

export type Business = Database['public']['Tables']['businesses']['Row']
export type NewBusiness = Database['public']['Tables']['businesses']['Insert']

export interface BusinessFormData {
  business_name: string
  contact_email: string
  contact_phone?: string
  website?: string
  linkedin?: string
  twitter?: string
  address_line_1?: string
  address_line_2?: string
  city?: string
  postcode?: string
  headquarters_location_id?: string | null
  customer_location_keys?: string[]
}

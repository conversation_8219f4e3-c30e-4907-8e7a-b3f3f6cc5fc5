# Net Zero Nexus Hub

A sustainability-focused platform connecting organizations and individuals committed to achieving net-zero emissions and environmental sustainability.

## Project Structure

This project follows a feature-based organization, where code is grouped by feature rather than by type. This makes it easier to navigate and maintain the codebase as it grows.

### Core Structure

```
src/
  features/           # Feature-based modules
    auth/             # Authentication feature
    businessDirectory/ # Business Directory feature
    events/           # Events feature
    fundingFinder/    # Funding Finder feature
    home/             # Home page feature
    jobs/             # Jobs feature
    members/          # Members directory feature
    profile/          # User profile feature
  components/         # Shared components used across features
  contexts/           # Application-wide contexts (e.g., AuthContext)
  hooks/              # Custom hooks
  integrations/       # External service integrations
  lib/                # Utility functions and helpers
  pages/              # Shared pages (like NotFound)
```

### Feature Structure

Each feature is organized with its own internal structure:

```
feature/
  components/         # Components specific to this feature
  hooks/              # Custom hooks specific to this feature
  pages/              # Page components for this feature
  utils/              # Utility functions specific to this feature
  index.tsx           # Entry point that exports main components
  README.md           # Documentation for the feature
```

### Feature Roadmap

The platform is being built in phases:

1. Core functionality (auth, profile, members)
2. Business Directory
3. Events
4. Funding Finder
5. Jobs

## Project info

**URL**: https://lovable.dev/projects/10f43a48-a1e8-4d3d-b4af-ec9ff8325e58

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/10f43a48-a1e8-4d3d-b4af-ec9ff8325e58) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase (Backend & Authentication)

## Supabase Setup

This project is connected to Supabase for backend services:

- **Project ID**: `uxxfqaabfktkupcqbrue`
- **Database**: PostgreSQL with user profiles
- **Authentication**: Built-in auth system
- **Edge Functions**: Custom server-side functions

### Edge Functions Deployed

- `delete-user`: Securely handles user account deletion

### Local Development with Supabase

If you need to work with Supabase functions locally:

```sh
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Deploy functions
supabase functions deploy delete-user --project-ref uxxfqaabfktkupcqbrue
```

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/10f43a48-a1e8-4d3d-b4af-ec9ff8325e58) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

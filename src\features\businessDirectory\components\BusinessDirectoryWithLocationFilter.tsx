import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { But<PERSON> } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { 
  Search, 
  MapPin, 
  Building2, 
  Users, 
  Filter, 
  X, 
  Loader2,
  ExternalLink 
} from 'lucide-react';
import LocationAccordion from '../../../components/LocationAccordion';
import { useBusinessLocationFilter, useLocationSearch } from '../../../hooks/useLocations';
import { LocationService } from '../../../services/locationService';
import type { BusinessWithLocations } from '../../../types/location.types';

const BusinessDirectoryWithLocationFilter = () => {
  // Filter state
  const [headquartersSearch, setHeadquartersSearch] = useState('');
  const [selectedHeadquarters, setSelectedHeadquarters] = useState<string[]>([]);
  const [customerLocationKeys, setCustomerLocationKeys] = useState<Set<string>>(new Set());
  const [businessNameFilter, setBusinessNameFilter] = useState('');
  const [activeFilters, setActiveFilters] = useState(false);

  // Hooks
  const { businesses, loading, error, filterBusinesses, clearFilter } = useBusinessLocationFilter();
  const { results: hqSearchResults, search: searchHQ } = useLocationSearch();

  // Handle headquarters location search
  const handleHQSearch = (query: string) => {
    setHeadquartersSearch(query);
    if (query.trim()) {
      searchHQ(query);
    }
  };

  const addHeadquartersLocation = (locationId: string, locationName: string) => {
    if (!selectedHeadquarters.includes(locationId)) {
      setSelectedHeadquarters([...selectedHeadquarters, locationId]);
    }
    setHeadquartersSearch('');
  };

  const removeHeadquartersLocation = (locationId: string) => {
    setSelectedHeadquarters(selectedHeadquarters.filter(id => id !== locationId));
  };

  // Apply filters
  const applyFilters = async () => {
    try {
      // Convert customer location keys to IDs
      const customerLocationIds = customerLocationKeys.size > 0 
        ? await LocationService.convertSelectionKeysToIds(Array.from(customerLocationKeys))
        : undefined;

      await filterBusinesses(
        selectedHeadquarters.length > 0 ? selectedHeadquarters : undefined,
        customerLocationIds
      );
      
      setActiveFilters(true);
    } catch (error) {
      console.error('Failed to apply filters:', error);
    }
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedHeadquarters([]);
    setCustomerLocationKeys(new Set());
    setBusinessNameFilter('');
    setHeadquartersSearch('');
    setActiveFilters(false);
    clearFilter();
  };

  // Filter businesses by name (client-side)
  const filteredBusinesses = businesses.filter(business => 
    !businessNameFilter || 
    business.business_name.toLowerCase().includes(businessNameFilter.toLowerCase())
  );

  // Get filter count
  const getFilterCount = () => {
    return selectedHeadquarters.length + customerLocationKeys.size + (businessNameFilter ? 1 : 0);
  };

  const BusinessCard = ({ business }: { business: BusinessWithLocations }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Building2 className="w-5 h-5 text-blue-600" />
            <span>{business.business_name}</span>
          </div>
          <Button variant="ghost" size="sm">
            <ExternalLink className="w-4 h-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Headquarters */}
        {business.headquarters_name && (
          <div className="flex items-start space-x-2">
            <MapPin className="w-4 h-4 text-green-600 mt-0.5" />
            <div>
              <div className="text-sm font-medium">Headquarters</div>
              <div className="text-sm text-gray-600">{business.headquarters_path || business.headquarters_name}</div>
            </div>
          </div>
        )}

        {/* Customer Locations */}
        {business.customer_locations && business.customer_locations.length > 0 && (
          <div className="flex items-start space-x-2">
            <Users className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <div className="text-sm font-medium mb-1">Customer Regions</div>
              <div className="flex flex-wrap gap-1">
                {business.customer_locations.slice(0, 3).map((location) => (
                  <Badge key={location.id} variant="secondary" className="text-xs">
                    {location.name}
                  </Badge>
                ))}
                {business.customer_locations.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{business.customer_locations.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">Business Directory</h1>
        <p className="text-gray-600">Find businesses by their location and customer reach</p>
      </div>

      {/* Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>Filters</span>
              {getFilterCount() > 0 && (
                <Badge variant="secondary">{getFilterCount()} active</Badge>
              )}
            </div>
            {getFilterCount() > 0 && (
              <Button variant="outline" size="sm" onClick={clearAllFilters}>
                <X className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="location" className="space-y-4">
            <TabsList>
              <TabsTrigger value="location">Location Filters</TabsTrigger>
              <TabsTrigger value="business">Business Filters</TabsTrigger>
            </TabsList>

            <TabsContent value="location" className="space-y-6">
              {/* Headquarters Filter */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="hq-search">Filter by Headquarters Location</Label>
                  <div className="mt-2 space-y-2">
                    <Input
                      id="hq-search"
                      placeholder="Search for headquarters locations..."
                      value={headquartersSearch}
                      onChange={(e) => handleHQSearch(e.target.value)}
                    />
                    
                    {/* HQ Search Results */}
                    {hqSearchResults.length > 0 && headquartersSearch && (
                      <div className="border border-gray-200 rounded-md max-h-48 overflow-y-auto">
                        {hqSearchResults.map((location) => (
                          <div
                            key={location.id}
                            className="p-3 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50"
                            onClick={() => addHeadquartersLocation(location.id, location.name)}
                          >
                            <div className="font-medium">{location.name}</div>
                            <div className="text-sm text-gray-600">{location.full_path}</div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Selected HQ Locations */}
                    {selectedHeadquarters.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {selectedHeadquarters.map((locationId) => {
                          const location = hqSearchResults.find(r => r.id === locationId);
                          return (
                            <Badge key={locationId} variant="secondary" className="flex items-center space-x-1">
                              <span>{location?.name || 'Unknown'}</span>
                              <button
                                onClick={() => removeHeadquartersLocation(locationId)}
                                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </Badge>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>

                {/* Customer Locations Filter */}
                <div>
                  <Label>Filter by Customer Locations</Label>
                  <div className="mt-2">
                    <LocationAccordion
                      selectedItems={customerLocationKeys}
                      onSelectionChange={setCustomerLocationKeys}
                      title="Customer Regions"
                      subtitle="Find businesses that serve customers in these areas"
                      allowMultiple={true}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="business" className="space-y-4">
              <div>
                <Label htmlFor="business-name">Business Name</Label>
                <Input
                  id="business-name"
                  placeholder="Search business names..."
                  value={businessNameFilter}
                  onChange={(e) => setBusinessNameFilter(e.target.value)}
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Apply Filters Button */}
          <div className="pt-4 border-t">
            <Button onClick={applyFilters} disabled={loading} className="w-full sm:w-auto">
              {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              <Search className="w-4 h-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      {activeFilters && (
        <div className="space-y-4">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {loading ? 'Searching...' : `${filteredBusinesses.length} businesses found`}
            </h2>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Searching businesses...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <Card>
              <CardContent className="text-center py-8 text-red-600">
                <p>Error searching businesses: {error}</p>
                <Button variant="outline" onClick={applyFilters} className="mt-2">
                  Try Again
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Results Grid */}
          {!loading && !error && (
            <>
              {filteredBusinesses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredBusinesses.map((business) => (
                    <BusinessCard key={business.id} business={business} />
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-8 text-gray-500">
                    <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No businesses found matching your criteria</p>
                    <Button variant="outline" onClick={clearAllFilters} className="mt-2">
                      Clear Filters
                    </Button>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </div>
      )}

      {/* Initial State */}
      {!activeFilters && (
        <Card>
          <CardContent className="text-center py-12 text-gray-500">
            <Filter className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">Find Businesses by Location</h3>
            <p className="mb-4">Use the filters above to search for businesses by their headquarters or customer locations</p>
            <Button onClick={applyFilters}>
              <Search className="w-4 h-4 mr-2" />
              Start Searching
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BusinessDirectoryWithLocationFilter;
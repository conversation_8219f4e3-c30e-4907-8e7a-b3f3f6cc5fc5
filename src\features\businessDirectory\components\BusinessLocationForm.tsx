import { useState, useEffect } from 'react';
import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Label } from '../../../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { Alert, AlertDescription } from '../../../components/ui/alert';
import { Building2, MapPin, Users, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import LocationAccordion from '../../../components/LocationAccordion';
import { useBusinessLocations, useLocationSearch } from '../../../hooks/useLocations';
import { useToast } from '../../../hooks/use-toast';
import type { LocationSearchResult } from '../../../types/location.types';

interface BusinessLocationFormProps {
  businessId: string;
  onSave?: () => void;
  onCancel?: () => void;
  readonly?: boolean;
}

const BusinessLocationForm = ({
  businessId,
  onSave,
  onCancel,
  readonly = false
}: BusinessLocationFormProps) => {
  const { toast } = useToast();
  
  // Location data and state
  const { business, loading, error, updating, updateAllLocations, reload } = useBusinessLocations(businessId);
  const { results: searchResults, search, clearResults } = useLocationSearch();
  
  // Form state
  const [selectedHeadquarters, setSelectedHeadquarters] = useState<string | null>(null);
  const [customerLocationKeys, setCustomerLocationKeys] = useState<Set<string>>(new Set());
  const [headquartersSearch, setHeadquartersSearch] = useState('');
  const [hasChanges, setHasChanges] = useState(false);
  
  // Initialize form with business data
  useEffect(() => {
    if (business) {
      setSelectedHeadquarters(business.headquarters_id);
      
      // Convert customer locations back to selection keys
      if (business.customer_locations) {
        const keys = new Set<string>();
        business.customer_locations.forEach(location => {
          // Create a key that matches the accordion format
          // This is a simplified version - you might need to enhance this based on your exact key format
          const key = location.slug; // You may need to construct this differently
          keys.add(key);
        });
        setCustomerLocationKeys(keys);
      }
      setHasChanges(false);
    }
  }, [business]);

  // Check for changes
  useEffect(() => {
    if (business) {
      const headquartersChanged = selectedHeadquarters !== business.headquarters_id;
      const customerLocationsChanged = customerLocationKeys.size !== (business.customer_locations?.length || 0);
      setHasChanges(headquartersChanged || customerLocationsChanged);
    }
  }, [selectedHeadquarters, customerLocationKeys, business]);

  // Handle headquarters search
  useEffect(() => {
    if (headquartersSearch.trim()) {
      search(headquartersSearch);
    } else {
      clearResults();
    }
  }, [headquartersSearch, search, clearResults]);

  const handleSave = async () => {
    if (!business || !hasChanges) return;

    try {
      await updateAllLocations(
        selectedHeadquarters,
        Array.from(customerLocationKeys)
      );
      
      toast({
        title: "Success",
        description: "Business locations updated successfully",
        variant: "default",
      });
      
      setHasChanges(false);
      onSave?.();
    } catch (error) {
      console.error('Failed to save locations:', error);
      toast({
        title: "Error",
        description: "Failed to update business locations. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCancel = () => {
    if (business) {
      setSelectedHeadquarters(business.headquarters_id);
      setCustomerLocationKeys(new Set());
      setHasChanges(false);
    }
    onCancel?.();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading business locations...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load business locations: {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={reload}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!business) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Business not found
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Business Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="w-5 h-5" />
            <span>{business.business_name}</span>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Headquarters Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="w-5 h-5" />
            <span>Headquarters Location</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="headquarters-search">Search for headquarters location</Label>
            <div className="relative">
              <input
                id="headquarters-search"
                type="text"
                placeholder="Search counties, regions, or areas..."
                value={headquartersSearch}
                onChange={(e) => setHeadquartersSearch(e.target.value)}
                disabled={readonly}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100"
              />
            </div>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="space-y-2">
              <Label>Select headquarters location:</Label>
              <div className="border border-gray-200 rounded-md max-h-48 overflow-y-auto">
                {searchResults.map((location) => (
                  <div
                    key={location.id}
                    className={`p-3 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50 ${
                      selectedHeadquarters === location.id ? 'bg-blue-50 border-blue-200' : ''
                    } ${readonly ? 'cursor-not-allowed opacity-60' : ''}`}
                    onClick={() => !readonly && setSelectedHeadquarters(location.id)}
                  >
                    <div className="font-medium">{location.name}</div>
                    <div className="text-sm text-gray-600">{location.full_path}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Current Selection */}
          {business.headquarters_name && (
            <div className="p-3 bg-gray-50 rounded-md">
              <div className="text-sm text-gray-600">Current headquarters:</div>
              <div className="font-medium">{business.headquarters_name}</div>
              {business.headquarters_path && (
                <div className="text-sm text-gray-500">{business.headquarters_path}</div>
              )}
            </div>
          )}

          {selectedHeadquarters && selectedHeadquarters !== business.headquarters_id && (
            <div className="p-3 bg-green-50 rounded-md border border-green-200">
              <div className="flex items-center space-x-2 text-green-800">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">New headquarters selected</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Customer Locations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Customer Locations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Select the regions where your business currently has customers or operates.
            </p>
            
            <LocationAccordion
              selectedItems={customerLocationKeys}
              onSelectionChange={setCustomerLocationKeys}
              title="Customer Regions"
              subtitle="Select all regions where your business has customers"
              allowMultiple={true}
              disabled={readonly}
            />
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      {!readonly && (
        <div className="flex items-center justify-between pt-6 border-t">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            {hasChanges && (
              <>
                <AlertCircle className="w-4 h-4 text-amber-500" />
                <span>You have unsaved changes</span>
              </>
            )}
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={updating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges || updating}
            >
              {updating && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Save Changes
            </Button>
          </div>
        </div>
      )}

      {/* Read-only mode info */}
      {readonly && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Location information is in read-only mode
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default BusinessLocationForm;

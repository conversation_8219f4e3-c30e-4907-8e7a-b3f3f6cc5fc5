import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, Plus, List } from "lucide-react";
import { useAuth } from '@/contexts/AuthContext';
import BusinessForm from './BusinessForm';
import BusinessList from './BusinessList';

interface BusinessManagementProps {
  defaultTab?: 'add' | 'list';
}

const BusinessManagement: React.FC<BusinessManagementProps> = ({ defaultTab = 'add' }) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const { user } = useAuth();

  const handleBusinessSubmit = () => {
    // After successful business creation, switch to the list tab
    setActiveTab('list');
  };

  // Don't render if no user is authenticated
  if (!user) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">Please sign in to manage businesses.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Business Management</h1>
        <p className="text-gray-600">Manage your business listings and information</p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'add' | 'list')}>
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="add" className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add Business
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="w-4 h-4" />
            Your Businesses
          </TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="add" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Add New Business
                </CardTitle>
              </CardHeader>
              <CardContent>
                <BusinessForm onSubmit={handleBusinessSubmit} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="list" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <List className="w-5 h-5" />
                  Your Business Listings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <BusinessList userId={user.id} />
              </CardContent>
            </Card>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default BusinessManagement;

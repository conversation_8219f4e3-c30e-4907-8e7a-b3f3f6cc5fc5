import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, X, SortAsc, SortDesc, Calendar } from "lucide-react";

export type SortOption = 'name_asc' | 'name_desc' | 'date_asc' | 'date_desc';

interface BusinessSearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  locationFilter: string;
  onLocationFilterChange: (value: string) => void;
  sortBy: SortOption;
  onSortChange: (value: SortOption) => void;
  showFilters: boolean;
  onToggleFilters: () => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  locationOptions: {
    cities: string[];
    postcodes: string[];
  };
  resultCount: number;
  totalCount: number;
}

const BusinessSearchAndFilter: React.FC<BusinessSearchAndFilterProps> = ({
  searchTerm,
  onSearchChange,
  locationFilter,
  onLocationFilterChange,
  sortBy,
  onSortChange,
  showFilters,
  onToggleFilters,
  onClearFilters,
  hasActiveFilters,
  locationOptions,
  resultCount,
  totalCount,
}) => {
  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search & Filter
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {resultCount} of {totalCount} businesses
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleFilters}
            >
              <Filter className="w-4 h-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search businesses by name, email, location, or website..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
            {/* Location Filter */}
            <div className="space-y-2">
              <Label htmlFor="location-filter">Filter by Location</Label>
              <Select value={locationFilter} onValueChange={onLocationFilterChange}>
                <SelectTrigger id="location-filter">
                  <SelectValue placeholder="All locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All locations</SelectItem>
                  {locationOptions.cities.map(city => (
                    <SelectItem key={`city-${city}`} value={city}>
                      📍 {city}
                    </SelectItem>
                  ))}
                  {locationOptions.postcodes.map(postcode => (
                    <SelectItem key={`postcode-${postcode}`} value={postcode}>
                      📮 {postcode}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort Options */}
            <div className="space-y-2">
              <Label htmlFor="sort-by">Sort by</Label>
              <Select value={sortBy} onValueChange={onSortChange}>
                <SelectTrigger id="sort-by">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name_asc">
                    <div className="flex items-center gap-2">
                      <SortAsc className="w-4 h-4" />
                      Name A-Z
                    </div>
                  </SelectItem>
                  <SelectItem value="name_desc">
                    <div className="flex items-center gap-2">
                      <SortDesc className="w-4 h-4" />
                      Name Z-A
                    </div>
                  </SelectItem>
                  <SelectItem value="date_desc">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Newest first
                    </div>
                  </SelectItem>
                  <SelectItem value="date_asc">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Oldest first
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters */}
            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                variant="outline" 
                onClick={onClearFilters}
                disabled={!hasActiveFilters}
                className="w-full"
              >
                <X className="w-4 h-4 mr-2" />
                Clear Filters
              </Button>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            {searchTerm && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: "{searchTerm}"
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onSearchChange('')}
                />
              </Badge>
            )}
            {locationFilter && locationFilter !== 'all' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Location: {locationFilter}
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onLocationFilterChange('all')}
                />
              </Badge>
            )}
            {sortBy !== 'name_asc' && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Sort: {sortBy.replace('_', ' ')}
                <X 
                  className="w-3 h-3 cursor-pointer hover:bg-muted rounded-full" 
                  onClick={() => onSortChange('name_asc')}
                />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BusinessSearchAndFilter;

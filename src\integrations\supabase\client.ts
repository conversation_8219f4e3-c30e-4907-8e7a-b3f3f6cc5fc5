// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Default to remote URLs (production)
const REMOTE_SUPABASE_URL = "https://uxxfqaabfktkupcqbrue.supabase.co";
const REMOTE_SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV4eGZxYWFiZmt0a3VwY3FicnVlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTE5NDUsImV4cCI6MjA2NTk2Nzk0NX0.z9ADGjM6bIjKy0TlH6NiFbNPGj6jYJdz_r72nbmQqEU";

// Local Supabase URLs (development)
const LOCAL_SUPABASE_URL = "http://127.0.0.1:54321";
const LOCAL_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0";

// Use environment variables or fall back to defaults
const supabaseMode = import.meta.env.VITE_SUPABASE_MODE || 'remote';
const SUPABASE_URL = supabaseMode === 'local' 
  ? (import.meta.env.VITE_SUPABASE_URL || LOCAL_SUPABASE_URL)
  : REMOTE_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY = supabaseMode === 'local'
  ? (import.meta.env.VITE_SUPABASE_ANON_KEY || LOCAL_SUPABASE_ANON_KEY)
  : REMOTE_SUPABASE_PUBLISHABLE_KEY;

console.log(`🔗 Connecting to Supabase in ${supabaseMode} mode:`, SUPABASE_URL);

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UserCheck, Calendar, Shield, Building2, Briefcase, MapPin } from "lucide-react";
import AvatarDisplay from "@/features/profile/components/AvatarDisplay";
import { useNavigate } from "react-router-dom";

// Define the member type for the component
export type MemberItem = {
  id: string;
  first_name: string | null;
  last_name: string | null;
  joinedAt: string;
  job_title: string | null;
  organisation_name: string | null;
  bio: string | null;
  avatar_url: string | null;
  sustainability_professional: boolean;
  linkedin_url: string | null;
  twitter_url: string | null;
  instagram_url: string | null;
  tiktok_url: string | null;
  location_id: string | null;
  location_name?: string | null;
};

interface MembersListProps {
  members: MemberItem[];
}

const MembersList = ({ members }: MembersListProps) => {
  const navigate = useNavigate();

  const handleMemberClick = (memberId: string) => {
    navigate(`/members/${memberId}`);
  };

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {members.map((member) => (
        <Card 
          key={member.id} 
          className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
          onClick={() => handleMemberClick(member.id)}
        >
          <CardHeader className="pb-3 relative">
            <div className="flex items-start gap-3">
              <AvatarDisplay
                avatarUrl={member.avatar_url}
                firstName={member.first_name}
                lastName={member.last_name}
                email={`${member.first_name?.toLowerCase() || ''}${member.last_name?.toLowerCase() || ''}@example.com`}
                size="lg"
                shape="square"
              />
              <div className="flex-1 min-w-0 pr-2">
                <CardTitle className="text-lg leading-tight">
                  {`${member.first_name || ''} ${member.last_name || ''}`.trim() || 'Anonymous User'}
                </CardTitle>
                {member.job_title && (
                  <div className="flex items-center gap-2 text-base font-semibold mt-2 text-zinc-700">
                    <Briefcase className="w-5 h-5 text-primary" />
                    {member.job_title}
                  </div>
                )}
                <div className="flex items-center gap-2 text-base font-semibold mt-1 text-zinc-700">
                  <Building2 className="w-5 h-5 text-primary" />
                  {member.organisation_name ? member.organisation_name : <span className="italic text-muted-foreground">None</span>}
                </div>
                <div className="flex items-center gap-2 text-base font-semibold mt-1 text-emerald-700">
                  <MapPin className="w-5 h-5 text-emerald-700" />
                  {member.location_name ? member.location_name : <span className="italic text-muted-foreground">None</span>}
                </div>
              </div>
            </div>
            {member.sustainability_professional && (
              <div className="mt-3">
                <Badge className="bg-emerald-600 hover:bg-emerald-700 text-white">
                  <Shield className="w-3 h-3 mr-1" />
                  Sustainability Professional
                </Badge>
              </div>
            )}
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-2">
              {member.bio && (
                <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                  {member.bio}
                </p>
              )}
              <div className="flex items-center gap-1 text-muted-foreground text-sm">
                <Calendar className="w-3 h-3" />
                Joined {new Date(member.joinedAt).toLocaleDateString()}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default MembersList;

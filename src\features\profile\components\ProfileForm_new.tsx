import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import AvatarUpload from './AvatarUpload';
import { cloudflareService } from '@/services/cloudflareService';

interface ProfileFormProps {
  id: string;
  initialFirstName: string;
  initialLastName: string;
  initialJobTitle: string;
  initialSustainabilityProfessional: boolean;
  initialAvatarUrl?: string | null;
  initialAvatarCloudflareKey?: string | null;
  onProfileUpdated: () => void;
}

const ProfileForm = ({
  id,
  initialFirstName,
  initialLastName,
  initialJobTitle,
  initialSustainabilityProfessional,
  initialAvatarUrl,
  initialAvatarCloudflareKey,
  onProfileUpdated
}: ProfileFormProps) => {
  const [firstName, setFirstName] = useState(initialFirstName);
  const [lastName, setLastName] = useState(initialLastName);
  const [jobTitle, setJobTitle] = useState(initialJobTitle);
  const [sustainabilityProfessional, setSustainabilityProfessional] = useState(initialSustainabilityProfessional);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(initialAvatarUrl || null);
  const [avatarCloudflareKey, setAvatarCloudflareKey] = useState<string | null>(initialAvatarCloudflareKey || null);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setSaving(true);
    
    try {
      // If we're removing an avatar, delete the old one from Cloudflare
      if (initialAvatarCloudflareKey && !avatarCloudflareKey) {
        await cloudflareService.deleteAvatar(initialAvatarCloudflareKey);
      }
      
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName.trim() || null,
          last_name: lastName.trim() || null,
          job_title: jobTitle.trim() || null,
          sustainability_professional: sustainabilityProfessional,
          avatar_url: avatarUrl,
          avatar_cloudflare_key: avatarCloudflareKey,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        toast({
          title: "Error",
          description: "Failed to update profile",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Success",
          description: "Profile updated successfully"
        });
        onProfileUpdated();
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAvatarUpdate = (url: string | null, key: string | null) => {
    setAvatarUrl(url);
    setAvatarCloudflareKey(key);
  };

  return (
    <form onSubmit={handleSave} className="space-y-6">
      {/* Avatar Upload Section */}
      <AvatarUpload
        currentAvatarUrl={avatarUrl}
        onAvatarUpdate={handleAvatarUpdate}
        userId={id}
        firstName={firstName}
        lastName={lastName}
      />

      {/* Existing Form Fields */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            placeholder="Enter your first name"
          />
        </div>
        
        <div>
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            placeholder="Enter your last name"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="jobTitle">Job Title</Label>
        <Input
          id="jobTitle"
          value={jobTitle}
          onChange={(e) => setJobTitle(e.target.value)}
          placeholder="Enter your job title"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="sustainability"
          checked={sustainabilityProfessional}
          onCheckedChange={(checked) => setSustainabilityProfessional(checked === true)}
        />
        <Label htmlFor="sustainability" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          I am a sustainability professional
        </Label>
      </div>

      <Button type="submit" disabled={saving} className="w-full">
        {saving ? 'Saving...' : 'Save Changes'}
      </Button>
    </form>
  );
};

export default ProfileForm;

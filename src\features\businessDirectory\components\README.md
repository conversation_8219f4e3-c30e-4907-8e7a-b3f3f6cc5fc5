# Business Images Feature

This feature allows businesses to upload and manage their logo and product images using Cloudflare R2 storage.

## Features

### Business Logo
- Upload a single logo image per business
- Replace existing logo
- Remove logo
- Displays in business list

### Product Images  
- Upload up to 10 product images per business (for basic users)
- Add optional descriptions to images
- Remove individual images
- Images stored as JSON array in database

## Architecture

### Database Schema
```sql
-- Added to businesses table:
logo_url TEXT                    -- Public Cloudflare URL
logo_cloudflare_key TEXT        -- Key for deletion
product_images JSONB DEFAULT '[]' -- Array of ProductImage objects
```

### ProductImage Structure
```typescript
interface ProductImage {
  id: string;
  url: string;
  cloudflareKey: string;
  filename: string;
  description?: string;
  uploadedAt: string;
  order: number;
}
```

### File Organization in Cloudflare R2
```
business-logos/
  └── {businessId}/
      └── {timestamp}.{ext}

business-products/
  └── {businessId}/
      └── {timestamp}.{ext}
```

## Components

### BusinessImageManager
Main component for uploading and managing business images.

**Props:**
- `business: Business` - Business object with image data
- `onImagesUpdated: () => void` - Callback when images change

**Features:**
- Logo upload/replace/remove
- Product image upload with descriptions
- Image previews with delete buttons
- Loading states and error handling

### BusinessList (Updated)
Shows business logos in the list view and includes "Manage Images" button.

## Usage

1. **Access**: Click the Images icon on any business card
2. **Upload Logo**: Use "Upload Logo" button in the Logo section
3. **Add Product Images**: 
   - Optionally add description
   - Click "Add Product Image"
   - Select image file
4. **Remove Images**: Click X button on image previews

## Future Enhancements

### For Paid Users
- Separate `business_images` table for unlimited images
- Categories (gallery, team, office, etc.)
- Bulk upload
- Image reordering
- Advanced metadata
- Featured images

### Migration Path
When users upgrade, JSON data can be migrated to the advanced table structure.

## File Cleanup

Images are automatically queued for cleanup when:
- Logo is replaced
- Product images are removed  
- Business is deleted

Cleanup is handled by triggers that add entries to `business_image_cleanup_queue` table.

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface AvatarDisplayProps {
  avatarUrl?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  shape?: 'circle' | 'square';
}

const AvatarDisplay: React.FC<AvatarDisplayProps> = ({
  avatarUrl,
  firstName,
  lastName,
  email,
  size = 'md',
  className = '',
  shape = 'circle',
}) => {



  // Generate initials for fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }
    if (firstName) {
      return firstName.charAt(0).toUpperCase();
    }
    if (email) {
      return email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  // Size mappings with mobile responsive sizing
  const sizeClasses = {
    sm: 'h-8 w-8 sm:h-12 sm:w-12',
    md: 'h-12 w-12 sm:h-20 sm:w-20',
    lg: 'h-20 w-20 sm:h-32 sm:w-32',
    xl: 'h-24 w-24 sm:h-36 sm:w-36 lg:h-40 lg:w-40',
  };

  const textSizeClasses = {
    sm: 'text-xs sm:text-base',
    md: 'text-sm sm:text-lg',
    lg: 'text-lg sm:text-2xl',
    xl: 'text-xl sm:text-3xl',
  };

  // Shape classes
  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-xl',
  };

  return (
    <Avatar className={`${sizeClasses[size]} ${shapeClasses[shape]} ${className} shrink-0`}>
      <AvatarImage 
        src={avatarUrl || undefined} 
        alt="Profile avatar"
        className={`${shapeClasses[shape]} object-cover`}
      />
      <AvatarFallback className={`${textSizeClasses[size]} font-medium bg-muted ${shapeClasses[shape]}`}>
        {getInitials()}
      </AvatarFallback>
    </Avatar>
  );
};

export default AvatarDisplay;

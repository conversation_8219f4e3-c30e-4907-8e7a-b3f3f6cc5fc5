-- Populate Net-Zero Categories with the provided data structure
-- This migration inserts the 7 main categories and their subcategories

-- Insert main categories
INSERT INTO public.netzero_categories (name, description, sort_order) VALUES
('Energy', 'Energy-related net-zero initiatives including renewable energy, efficiency, and storage', 1),
('Transportation', 'Transportation and mobility solutions for net-zero emissions', 2),
('Buildings and Facilities', 'Building construction, retrofitting, and facility management for net-zero', 3),
('Manufacturing and Operations', 'Manufacturing processes and operational improvements for net-zero', 4),
('Agriculture and Land Use', 'Agricultural practices and land management for carbon reduction', 5),
('Carbon Management', 'Direct carbon management, measurement, and offsetting strategies', 6),
('Strategy, Finance and Governance', 'Strategic planning, financing, and governance for net-zero initiatives', 7);

-- Insert subcategories for Energy
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Energy'), 'Renewable Energy', 'Solar, wind, hydro, geothermal energy solutions', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Energy'), 'Energy Efficiency', 'Energy upgrades and smart management systems', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Energy'), 'Energy Storage', 'Battery systems and thermal storage solutions', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Energy'), 'Grid Decarbonization', 'Green electricity purchasing and grid improvements', 4);

-- Insert subcategories for Transportation
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Transportation'), 'Fleet Electrification', 'Electric vehicles and charging infrastructure', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Transportation'), 'Sustainable Fuels', 'Biofuels and hydrogen fuel solutions', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Transportation'), 'Logistics Optimization', 'Route planning and modal shift strategies', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Transportation'), 'Employee Commuting and Business Travel', 'Commuting incentives and virtual meeting solutions', 4);

-- Insert subcategories for Buildings and Facilities
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Buildings and Facilities'), 'Green Construction and Retrofitting', 'Low-carbon materials and insulation solutions', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Buildings and Facilities'), 'HVAC and Building Systems', 'Efficient heating, cooling, and smart building controls', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Buildings and Facilities'), 'On-site Renewables', 'Solar panels and heat pump installations', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Buildings and Facilities'), 'Water and Waste Management', 'Water reduction, recycling, and efficiency systems', 4);

-- Insert subcategories for Manufacturing and Operations
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Manufacturing and Operations'), 'Clean Production', 'Process electrification and low-carbon heat solutions', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Manufacturing and Operations'), 'Sustainable Materials', 'Recycled, bio-based, and low-carbon input materials', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Manufacturing and Operations'), 'Process Optimization', 'Resource efficiency and waste minimization', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Manufacturing and Operations'), 'Circular Economy Practices', 'Reuse, remanufacture, and product life extension', 4);

-- Insert subcategories for Agriculture and Land Use
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Agriculture and Land Use'), 'Sustainable Agriculture', 'Soil health improvement and fertilizer management', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Agriculture and Land Use'), 'Livestock and Manure Management', 'Methane reduction strategies', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Agriculture and Land Use'), 'Land Restoration and Forestry', 'Tree planting and deforestation prevention', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Agriculture and Land Use'), 'Carbon Sinks', 'Agroforestry and peatland protection', 4);

-- Insert subcategories for Carbon Management
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Carbon Management'), 'Emissions Measurement and Reporting', 'Carbon footprint analysis and reporting tools', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Carbon Management'), 'Carbon Offsetting', 'Nature-based and technological carbon removal', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Carbon Management'), 'Carbon Capture, Utilization and Storage', 'CCUS technologies and implementation', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Carbon Management'), 'Beyond Value Chain Mitigation', 'Supporting external climate projects', 4);

-- Insert subcategories for Strategy, Finance and Governance
INSERT INTO public.netzero_subcategories (category_id, name, description, sort_order) VALUES
((SELECT id FROM public.netzero_categories WHERE name = 'Strategy, Finance and Governance'), 'Net Zero Planning', 'Goal setting and roadmap development', 1),
((SELECT id FROM public.netzero_categories WHERE name = 'Strategy, Finance and Governance'), 'ESG Compliance and Reporting', 'ESG disclosures and framework implementation', 2),
((SELECT id FROM public.netzero_categories WHERE name = 'Strategy, Finance and Governance'), 'Green Finance', 'Sustainable investment and green bond solutions', 3),
((SELECT id FROM public.netzero_categories WHERE name = 'Strategy, Finance and Governance'), 'Stakeholder Engagement', 'Supply chain, customer, and regulatory engagement', 4);

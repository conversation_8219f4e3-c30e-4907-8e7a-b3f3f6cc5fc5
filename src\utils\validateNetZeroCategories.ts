// Validation utility for Net-Zero Categories implementation
// This can be used to test the implementation after database setup

import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import type { NetZeroCategoryWithSubcategories } from '@/types/netzero-categories.types';

export class NetZeroCategoryValidator {
  /**
   * Validate that all expected categories and subcategories exist
   */
  static async validateCategoryStructure(): Promise<{
    success: boolean;
    errors: string[];
    categories: NetZeroCategoryWithSubcategories[];
  }> {
    const errors: string[] = [];
    let categories: NetZeroCategoryWithSubcategories[] = [];

    try {
      // Fetch all categories
      categories = await NetZeroCategoryService.getAllCategoriesWithSubcategories();

      // Expected categories
      const expectedCategories = [
        'Energy',
        'Transportation', 
        'Buildings and Facilities',
        'Manufacturing and Operations',
        'Agriculture and Land Use',
        'Carbon Management',
        'Strategy, Finance and Governance'
      ];

      // Check if all expected categories exist
      const foundCategories = categories.map(cat => cat.name);
      for (const expected of expectedCategories) {
        if (!foundCategories.includes(expected)) {
          errors.push(`Missing category: ${expected}`);
        }
      }

      // Check if we have exactly 7 categories
      if (categories.length !== 7) {
        errors.push(`Expected 7 categories, found ${categories.length}`);
      }

      // Check each category has subcategories
      for (const category of categories) {
        if (!category.subcategories || category.subcategories.length === 0) {
          errors.push(`Category "${category.name}" has no subcategories`);
        } else if (category.subcategories.length !== 4) {
          errors.push(`Category "${category.name}" should have 4 subcategories, found ${category.subcategories.length}`);
        }
      }

      // Validate specific subcategories for Energy category
      const energyCategory = categories.find(cat => cat.name === 'Energy');
      if (energyCategory) {
        const expectedEnergySubcategories = [
          'Renewable Energy',
          'Energy Efficiency', 
          'Energy Storage',
          'Grid Decarbonization'
        ];
        
        const foundEnergySubcategories = energyCategory.subcategories.map(sub => sub.name);
        for (const expected of expectedEnergySubcategories) {
          if (!foundEnergySubcategories.includes(expected)) {
            errors.push(`Missing Energy subcategory: ${expected}`);
          }
        }
      }

    } catch (error) {
      errors.push(`Failed to fetch categories: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors,
      categories
    };
  }

  /**
   * Test user interests functionality
   */
  static async testUserInterests(userId: string, testSubcategoryIds: string[]): Promise<{
    success: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // Test updating user interests
      await NetZeroCategoryService.updateUserInterests(userId, testSubcategoryIds);

      // Test fetching user interests
      const { interests } = await NetZeroCategoryService.getUserInterests(userId);

      // Verify the interests were saved correctly
      if (interests.length !== testSubcategoryIds.length) {
        errors.push(`Expected ${testSubcategoryIds.length} interests, found ${interests.length}`);
      }

      const foundIds = interests.map(interest => interest.id);
      for (const expectedId of testSubcategoryIds) {
        if (!foundIds.includes(expectedId)) {
          errors.push(`Missing interest ID: ${expectedId}`);
        }
      }

      // Test clearing interests
      await NetZeroCategoryService.updateUserInterests(userId, []);
      const { interests: clearedInterests } = await NetZeroCategoryService.getUserInterests(userId);
      
      if (clearedInterests.length !== 0) {
        errors.push(`Expected 0 interests after clearing, found ${clearedInterests.length}`);
      }

    } catch (error) {
      errors.push(`User interests test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Test business categories functionality
   */
  static async testBusinessCategories(
    businessId: string, 
    testSubcategoryIds: string[], 
    primarySubcategoryId?: string
  ): Promise<{
    success: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // Test updating business categories
      await NetZeroCategoryService.updateBusinessCategories(businessId, testSubcategoryIds, primarySubcategoryId);

      // Test fetching business categories
      const { categories, primary_category } = await NetZeroCategoryService.getBusinessCategories(businessId);

      // Verify the categories were saved correctly
      if (categories.length !== testSubcategoryIds.length) {
        errors.push(`Expected ${testSubcategoryIds.length} categories, found ${categories.length}`);
      }

      const foundIds = categories.map(category => category.id);
      for (const expectedId of testSubcategoryIds) {
        if (!foundIds.includes(expectedId)) {
          errors.push(`Missing category ID: ${expectedId}`);
        }
      }

      // Verify primary category
      if (primarySubcategoryId) {
        if (!primary_category) {
          errors.push('Expected primary category but none found');
        } else if (primary_category.id !== primarySubcategoryId) {
          errors.push(`Expected primary category ${primarySubcategoryId}, found ${primary_category.id}`);
        }
      } else if (primary_category) {
        errors.push('Found unexpected primary category');
      }

      // Test clearing categories
      await NetZeroCategoryService.updateBusinessCategories(businessId, []);
      const { categories: clearedCategories } = await NetZeroCategoryService.getBusinessCategories(businessId);
      
      if (clearedCategories.length !== 0) {
        errors.push(`Expected 0 categories after clearing, found ${clearedCategories.length}`);
      }

    } catch (error) {
      errors.push(`Business categories test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Run all validation tests
   */
  static async runAllTests(userId?: string, businessId?: string): Promise<{
    success: boolean;
    results: {
      structure: any;
      userInterests?: any;
      businessCategories?: any;
    };
  }> {
    console.log('🧪 Running Net-Zero Categories validation tests...');

    // Test 1: Category structure
    console.log('📋 Testing category structure...');
    const structureResult = await this.validateCategoryStructure();
    
    if (structureResult.success) {
      console.log('✅ Category structure validation passed');
    } else {
      console.log('❌ Category structure validation failed:', structureResult.errors);
    }

    const results: any = { structure: structureResult };

    // Test 2: User interests (if userId provided)
    if (userId && structureResult.categories.length > 0) {
      console.log('👤 Testing user interests...');
      const testSubcategoryIds = structureResult.categories
        .slice(0, 2)
        .flatMap(cat => cat.subcategories.slice(0, 1))
        .map(sub => sub.id);
      
      const userResult = await this.testUserInterests(userId, testSubcategoryIds);
      results.userInterests = userResult;
      
      if (userResult.success) {
        console.log('✅ User interests test passed');
      } else {
        console.log('❌ User interests test failed:', userResult.errors);
      }
    }

    // Test 3: Business categories (if businessId provided)
    if (businessId && structureResult.categories.length > 0) {
      console.log('🏢 Testing business categories...');
      const testSubcategoryIds = structureResult.categories
        .slice(0, 2)
        .flatMap(cat => cat.subcategories.slice(0, 1))
        .map(sub => sub.id);
      const primaryId = testSubcategoryIds[0];
      
      const businessResult = await this.testBusinessCategories(businessId, testSubcategoryIds, primaryId);
      results.businessCategories = businessResult;
      
      if (businessResult.success) {
        console.log('✅ Business categories test passed');
      } else {
        console.log('❌ Business categories test failed:', businessResult.errors);
      }
    }

    const allSuccess = Object.values(results).every((result: any) => result.success);
    
    if (allSuccess) {
      console.log('🎉 All tests passed! Net-Zero Categories implementation is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the errors above.');
    }

    return {
      success: allSuccess,
      results
    };
  }
}

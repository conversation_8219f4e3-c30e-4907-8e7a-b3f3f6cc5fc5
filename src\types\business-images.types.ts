// Business image types for the Net Zero Nexus Hub

export interface ProductImage {
  id: string;
  url: string;
  cloudflareKey: string;
  filename: string;
  description?: string;
  uploadedAt: string;
  order: number;
}

export interface BusinessWithImages {
  id: string;
  business_name: string;
  contact_email: string;
  contact_phone?: string;
  website?: string;
  linkedin?: string;
  twitter?: string;
  address_line_1?: string;
  address_line_2?: string;
  city?: string;
  postcode?: string;
  logo_url?: string;
  logo_cloudflare_key?: string;
  product_images: ProductImage[];
  owner_id: string;
  created_at: string;
  updated_at: string;
}

export interface BusinessImageUploadResult {
  url: string;
  key: string;
}

export interface BusinessImageUploadError {
  message: string;
  code?: string;
}

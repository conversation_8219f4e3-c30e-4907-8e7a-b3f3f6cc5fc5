import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Contexts and Hooks
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

// Integrations
import { supabase } from '@/integrations/supabase/client';

// UI Components
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Local Components
import AccountSettings from '../components/AccountSettings';
import Dashboard from '../components/Dashboard';
import Notifications from '../components/Notifications';
import ProfileForm from '../components/ProfileForm';
import Sidebar from '../components/Sidebar';
import { BusinessManagement } from '@/features/businessDirectory/components';

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  job_title: string | null;
  sustainability_professional: boolean;
  avatar_url: string | null;
  avatar_cloudflare_key: string | null;
  profile_visible: boolean | null;
  newsletter_subscribed: boolean | null;
  show_business_menu: boolean | null;
  bio: string | null;
  organisation_name: string | null;
  linkedin_url: string | null;
  twitter_url: string | null;
  instagram_url: string | null;
  tiktok_url: string | null;
  location_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

const UserProfilePage = () => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }
    
    fetchProfile();
  }, [user, navigate]);

  const fetchProfile = async () => {
    if (!user) return;
    
    setLoading(true);
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      toast({
        title: "Error",
        description: "Failed to load profile",
        variant: "destructive"
      });
    } else {
      setProfile(data as Profile);
    }
    setLoading(false);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  const refreshProfile = async () => {
    if (!user) return;
    await fetchProfile();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="py-12 px-4 sm:px-6 lg:px-8 min-h-screen bg-background">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">Your Dashboard</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div>
            <Sidebar 
              firstName={profile?.first_name}
              lastName={profile?.last_name}
              email={user?.email} 
              jobTitle={profile?.job_title}
              avatarUrl={profile?.avatar_url}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              showBusinessMenu={profile?.show_business_menu ?? true}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {activeTab === 'dashboard' && (
              <Dashboard />
            )}
            
            {activeTab === 'notifications' && (
              <Notifications />
            )}
            
            {activeTab === 'account' && user && (
              <AccountSettings 
                userId={user.id} 
                signOut={signOut}
                onSettingsUpdated={() => {
                  // Refresh profile to update sidebar visibility
                  fetchProfile();
                }}
              />
            )}
            
            {activeTab === 'profile' && (
              <Card>
                <CardHeader>
                  <CardTitle>Profile</CardTitle>
                  <CardDescription>
                    Manage your profile information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="mb-4">
                      <Label>Email</Label>
                      <Input
                        type="email"
                        value={user?.email || ''}
                        disabled
                      />
                    </div>
                    
                    {profile && (
                      <ProfileForm
                        id={user!.id}
                        initialFirstName={profile.first_name || ''}
                        initialLastName={profile.last_name || ''}
                        initialJobTitle={profile.job_title || ''}
                        initialSustainabilityProfessional={profile.sustainability_professional || false}
                        initialAvatarUrl={profile.avatar_url}
                        initialAvatarCloudflareKey={profile.avatar_cloudflare_key}
                        initialBio={profile.bio}
                        initialOrganisationName={profile.organisation_name}
                        initialLinkedinUrl={profile.linkedin_url}
                        initialTwitterUrl={profile.twitter_url}
                        initialInstagramUrl={profile.instagram_url}
                        initialTiktokUrl={profile.tiktok_url}
                        initialLocation={profile.location_id}
                        onProfileUpdated={fetchProfile}
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === 'business' && profile?.show_business_menu && (
              <BusinessManagement />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfilePage;

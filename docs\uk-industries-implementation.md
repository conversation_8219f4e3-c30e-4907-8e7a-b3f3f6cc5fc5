# UK Industries Implementation

This document outlines the implementation of the UK Industries system for the Net Zero Nexus Hub.

## Overview

The system allows users to select their current working industry (single select) and businesses to indicate their main industry (single select) and target industries they serve (multi-select). It includes 7 main industry categories with multiple subcategories each.

## Database Schema

### Tables Created

1. **uk_industries** - Hierarchical structure with parent and child industries
2. **profile_industry** - Junction table for user's current industry (single select)
3. **business_industry** - Junction table for business's main industry (single select)
4. **business_target_industries** - Junction table for business target industries (multi-select)

### Migrations

- `20250627160000_create_uk_industries.sql` - Creates the database schema with hierarchical structure
- `20250627160001_populate_uk_industries.sql` - Populates with the 7 main categories and their subcategories

## Components

### IndustrySelector
- Flexible selector supporting single, multi, or both selection modes
- Hierarchical display with collapsible parent categories
- Only child industries can be selected (not parent categories)
- Maximum selection limits
- Located: `src/components/industries/IndustrySelector.tsx`

### IndustryDisplay
- Display component for showing selected industries
- Multiple variants (card, inline, badge, list)
- Shows parent > child hierarchy
- Located: `src/components/industries/IndustryDisplay.tsx`

## Services

### UKIndustryService
- Handles all database operations for industries
- Methods for fetching hierarchical data and managing associations
- Located: `src/services/ukIndustryService.ts`

## Integration Points

### Profile Form
- Added industry selection (single select for user's current industry)
- Only child industries can be selected
- Located: `src/features/profile/components/ProfileForm.tsx`

### Business Form
- Added main industry selection (single select)
- Added target industries selection (multi-select, max 8)
- Both use child industries only
- Located: `src/features/businessDirectory/components/BusinessForm.tsx`

### Member Detail Page
- Displays user's current industry with parent > child format
- Located: `src/features/members/pages/MemberDetailPage.tsx`

### Business Detail Page
- Displays business main industry and target industries
- Shows hierarchical structure
- Located: `src/features/businessDirectory/pages/BusinessDetailPage.tsx`

## Industry Categories Structure

### 1. Manufacturing & Production
- Automotive Manufacturing
- Aerospace and Defense
- Pharmaceuticals and Biotechnology
- Food and Beverage Production
- Textiles and Clothing
- Steel and Metals
- Chemicals and Petrochemicals

### 2. Services
- Financial Services
- Professional Services
- Information Technology and Software
- Creative Industries
- Tourism and Hospitality
- Retail and Wholesale Trade
- Transportation and Logistics

### 3. Energy & Utilities
- Oil and Gas
- Renewable Energy
- Nuclear Power
- Electricity and Gas Utilities
- Water and Waste Management

### 4. Primary Industries
- Agriculture and Farming
- Fishing and Aquaculture
- Mining and Quarrying

### 5. Construction & Real Estate
- Residential and Commercial Construction
- Infrastructure Development
- Property Development and Management

### 6. Healthcare & Education
- National Health Service (NHS)
- Private Healthcare
- Education
- Research and Development

### 7. Social Care
- Elderly Care
- Child and Family Services
- Disability Support Services
- Mental Health Services
- Housing and Homelessness Support

## Key Features

### Hierarchical Structure
- Parent industries provide categorization
- Child industries are the selectable options
- Prevents overly broad selections

### Single vs Multi-Select
- **User Industry**: Single select (current working industry)
- **Business Main Industry**: Single select (primary business sector)
- **Business Target Industries**: Multi-select (markets served)

### Data Integrity
- Unique constraints prevent duplicate selections
- Cascading deletes maintain referential integrity
- Sort order ensures consistent display

## Testing Steps

1. **Database Setup**
   - Run migrations to create tables and populate data
   - Verify hierarchical structure

2. **Profile Form Testing**
   - Navigate to user profile
   - Select a current industry
   - Save and verify persistence

3. **Business Form Testing**
   - Create/edit a business
   - Select main industry and target industries
   - Save and verify persistence

4. **Display Testing**
   - View member detail page
   - View business detail page
   - Verify industries display with parent > child format

5. **Validation Testing**
   - Use `src/utils/validateUKIndustries.ts` to test service functions

## Usage Examples

### For Users
- "I work in **Services > Information Technology and Software**"
- Displayed as badge on member cards
- Full details on member profile page

### For Businesses
- Main Industry: "**Manufacturing & Production > Automotive Manufacturing**"
- Target Industries: 
  - "**Energy & Utilities > Renewable Energy**"
  - "**Services > Professional Services**"
  - "**Construction & Real Estate > Infrastructure Development**"

## Future Enhancements

- Industry-based filtering in directories
- Industry matching for networking
- Analytics on popular industries
- Industry-specific content recommendations
- Advanced search by industry combinations

## Best Practices

1. **Selection Logic**: Only child industries are selectable to ensure specificity
2. **Display Format**: Always show "Parent > Child" for clarity
3. **Validation**: Enforce selection limits and data integrity
4. **Performance**: Use indexes for efficient querying
5. **User Experience**: Collapsible categories for better navigation

# Profile Feature

The profile feature provides user profile management, account settings, and personal dashboard.

## Components

- `UserProfilePage`: The main user profile page with tabbed interface
- `AccountSettings`: Component for managing account settings like deletion
- `Dashboard`: User dashboard with personalized metrics and information
- `Notifications`: Component to handle user notifications
- `ProfileForm`: Form for updating user profile information
- `Projects`: User's projects management component
- `Sidebar`: Navigation sidebar for the profile section

## Functionality

- Profile information editing
- Account settings management
- Personal dashboard with insights and metrics
- Notifications management

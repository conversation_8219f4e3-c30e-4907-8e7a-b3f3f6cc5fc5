// Validation utility for UK Industries implementation
// This can be used to test the implementation after database setup

import { UKIndustryService } from '@/services/ukIndustryService';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';

export class UKIndustryValidator {
  /**
   * Validate that all expected industries exist
   */
  static async validateIndustryStructure(): Promise<{
    success: boolean;
    errors: string[];
    industries: UKIndustryWithChildren[];
  }> {
    const errors: string[] = [];
    let industries: UKIndustryWithChildren[] = [];

    try {
      // Fetch all industries
      industries = await UKIndustryService.getAllIndustriesWithChildren();

      // Expected parent industries
      const expectedParentIndustries = [
        'Manufacturing & Production',
        'Services',
        'Energy & Utilities',
        'Primary Industries',
        'Construction & Real Estate',
        'Healthcare & Education',
        'Social Care'
      ];

      // Check if all expected parent industries exist
      const foundParentIndustries = industries.map(ind => ind.name);
      for (const expected of expectedParentIndustries) {
        if (!foundParentIndustries.includes(expected)) {
          errors.push(`Missing parent industry: ${expected}`);
        }
      }

      // Check if we have exactly 7 parent industries
      if (industries.length !== 7) {
        errors.push(`Expected 7 parent industries, found ${industries.length}`);
      }

      // Check each parent industry has children
      for (const parentIndustry of industries) {
        if (!parentIndustry.children || parentIndustry.children.length === 0) {
          errors.push(`Parent industry "${parentIndustry.name}" has no child industries`);
        }
      }

      // Validate specific child industries for Manufacturing & Production
      const manufacturingIndustry = industries.find(ind => ind.name === 'Manufacturing & Production');
      if (manufacturingIndustry) {
        const expectedManufacturingChildren = [
          'Automotive Manufacturing',
          'Aerospace and Defense',
          'Pharmaceuticals and Biotechnology',
          'Food and Beverage Production',
          'Textiles and Clothing',
          'Steel and Metals',
          'Chemicals and Petrochemicals'
        ];
        
        const foundManufacturingChildren = manufacturingIndustry.children.map(child => child.name);
        for (const expected of expectedManufacturingChildren) {
          if (!foundManufacturingChildren.includes(expected)) {
            errors.push(`Missing Manufacturing child industry: ${expected}`);
          }
        }
      }

      // Validate Services industry children
      const servicesIndustry = industries.find(ind => ind.name === 'Services');
      if (servicesIndustry) {
        const expectedServicesChildren = [
          'Financial Services',
          'Professional Services',
          'Information Technology and Software',
          'Creative Industries',
          'Tourism and Hospitality',
          'Retail and Wholesale Trade',
          'Transportation and Logistics'
        ];
        
        const foundServicesChildren = servicesIndustry.children.map(child => child.name);
        for (const expected of expectedServicesChildren) {
          if (!foundServicesChildren.includes(expected)) {
            errors.push(`Missing Services child industry: ${expected}`);
          }
        }
      }

    } catch (error) {
      errors.push(`Failed to fetch industries: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors,
      industries
    };
  }

  /**
   * Test user industry functionality
   */
  static async testUserIndustry(userId: string, testIndustryId: string): Promise<{
    success: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // Test updating user industry
      await UKIndustryService.updateUserIndustry(userId, testIndustryId);

      // Test fetching user industry
      const { industry } = await UKIndustryService.getUserIndustry(userId);

      // Verify the industry was saved correctly
      if (!industry) {
        errors.push('No industry found after setting');
      } else if (industry.id !== testIndustryId) {
        errors.push(`Expected industry ID ${testIndustryId}, found ${industry.id}`);
      }

      // Test clearing industry
      await UKIndustryService.updateUserIndustry(userId, null);
      const { industry: clearedIndustry } = await UKIndustryService.getUserIndustry(userId);
      
      if (clearedIndustry !== null) {
        errors.push('Expected null industry after clearing, but found industry');
      }

    } catch (error) {
      errors.push(`User industry test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Test business industries functionality
   */
  static async testBusinessIndustries(
    businessId: string, 
    testMainIndustryId: string,
    testTargetIndustryIds: string[]
  ): Promise<{
    success: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // Test updating business main industry
      await UKIndustryService.updateBusinessMainIndustry(businessId, testMainIndustryId);

      // Test updating business target industries
      await UKIndustryService.updateBusinessTargetIndustries(businessId, testTargetIndustryIds);

      // Test fetching business industries
      const { main_industry, target_industries } = await UKIndustryService.getBusinessIndustries(businessId);

      // Verify main industry
      if (!main_industry) {
        errors.push('No main industry found after setting');
      } else if (main_industry.id !== testMainIndustryId) {
        errors.push(`Expected main industry ID ${testMainIndustryId}, found ${main_industry.id}`);
      }

      // Verify target industries
      if (target_industries.length !== testTargetIndustryIds.length) {
        errors.push(`Expected ${testTargetIndustryIds.length} target industries, found ${target_industries.length}`);
      }

      const foundTargetIds = target_industries.map(industry => industry.id);
      for (const expectedId of testTargetIndustryIds) {
        if (!foundTargetIds.includes(expectedId)) {
          errors.push(`Missing target industry ID: ${expectedId}`);
        }
      }

      // Test clearing industries
      await UKIndustryService.updateBusinessMainIndustry(businessId, null);
      await UKIndustryService.updateBusinessTargetIndustries(businessId, []);
      
      const { main_industry: clearedMain, target_industries: clearedTargets } = 
        await UKIndustryService.getBusinessIndustries(businessId);
      
      if (clearedMain !== null) {
        errors.push('Expected null main industry after clearing');
      }
      
      if (clearedTargets.length !== 0) {
        errors.push(`Expected 0 target industries after clearing, found ${clearedTargets.length}`);
      }

    } catch (error) {
      errors.push(`Business industries test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: errors.length === 0,
      errors
    };
  }

  /**
   * Run all validation tests
   */
  static async runAllTests(userId?: string, businessId?: string): Promise<{
    success: boolean;
    results: {
      structure: any;
      userIndustry?: any;
      businessIndustries?: any;
    };
  }> {
    console.log('🧪 Running UK Industries validation tests...');

    // Test 1: Industry structure
    console.log('🏭 Testing industry structure...');
    const structureResult = await this.validateIndustryStructure();
    
    if (structureResult.success) {
      console.log('✅ Industry structure validation passed');
    } else {
      console.log('❌ Industry structure validation failed:', structureResult.errors);
    }

    const results: any = { structure: structureResult };

    // Test 2: User industry (if userId provided)
    if (userId && structureResult.industries.length > 0) {
      console.log('👤 Testing user industry...');
      const testIndustryId = structureResult.industries[0].children[0]?.id;
      
      if (testIndustryId) {
        const userResult = await this.testUserIndustry(userId, testIndustryId);
        results.userIndustry = userResult;
        
        if (userResult.success) {
          console.log('✅ User industry test passed');
        } else {
          console.log('❌ User industry test failed:', userResult.errors);
        }
      } else {
        console.log('⚠️ No child industries found for user industry test');
      }
    }

    // Test 3: Business industries (if businessId provided)
    if (businessId && structureResult.industries.length > 0) {
      console.log('🏢 Testing business industries...');
      const testMainIndustryId = structureResult.industries[0].children[0]?.id;
      const testTargetIndustryIds = structureResult.industries
        .slice(0, 2)
        .flatMap(parent => parent.children.slice(0, 1))
        .map(child => child.id);
      
      if (testMainIndustryId && testTargetIndustryIds.length > 0) {
        const businessResult = await this.testBusinessIndustries(
          businessId, 
          testMainIndustryId, 
          testTargetIndustryIds
        );
        results.businessIndustries = businessResult;
        
        if (businessResult.success) {
          console.log('✅ Business industries test passed');
        } else {
          console.log('❌ Business industries test failed:', businessResult.errors);
        }
      } else {
        console.log('⚠️ No child industries found for business industries test');
      }
    }

    const allSuccess = Object.values(results).every((result: any) => result.success);
    
    if (allSuccess) {
      console.log('🎉 All tests passed! UK Industries implementation is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the errors above.');
    }

    return {
      success: allSuccess,
      results
    };
  }
}

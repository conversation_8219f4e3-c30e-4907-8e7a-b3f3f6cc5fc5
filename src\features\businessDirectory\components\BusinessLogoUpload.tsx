import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { cloudflareService } from '@/services/cloudflareService';
import { Upload, X, Building } from 'lucide-react';

interface BusinessLogoUploadProps {
  currentLogoUrl?: string | null;
  currentLogoCloudflareKey?: string | null;
  onLogoUpdate: (url: string | null, key: string | null) => void;
  businessId: string;
  businessName?: string;
}

const BusinessLogoUpload: React.FC<BusinessLogoUploadProps> = ({
  currentLogoUrl,
  currentLogoCloudflareKey,
  onLogoUpdate,
  businessId,
  businessName,
}) => {
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentLogoUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Generate initials for fallback
  const getInitials = () => {
    if (businessName) {
      return businessName.charAt(0).toUpperCase();
    }
    return 'B';
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setUploading(true);

      // Create preview
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Delete old logo from Cloudflare R2 if it exists
      if (currentLogoCloudflareKey) {
        try {
          await cloudflareService.deleteBusinessImage(currentLogoCloudflareKey);
        } catch (deleteError) {
          console.error('Error deleting old logo:', deleteError);
          // Continue with upload even if deletion fails
        }
      }

      // Upload new logo to Cloudflare
      const result = await cloudflareService.uploadBusinessLogo(file, businessId);

      // Clean up preview URL
      URL.revokeObjectURL(objectUrl);

      // Update with real URL
      setPreviewUrl(result.url);
      onLogoUpdate(result.url, result.key);

      toast({
        title: "Logo ready",
        description: "Click 'Save Changes' to update your business",
      });

    } catch (error) {
      // Clean up preview URL on error
      if (previewUrl && previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(previewUrl);
      }
      
      // Revert preview
      setPreviewUrl(currentLogoUrl || null);

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload logo",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveLogo = () => {
    setPreviewUrl(null);
    onLogoUpdate(null, null);
    
    toast({
      title: "Logo removed",
      description: "Click 'Save Changes' to update your business",
    });
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        {/* Logo Display */}
        <div className="relative">
          <div className="h-20 w-20 rounded-lg border bg-muted flex items-center justify-center overflow-hidden">
            {previewUrl ? (
              <img 
                src={previewUrl} 
                alt="Business logo" 
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="flex flex-col items-center justify-center text-muted-foreground">
                <Building className="h-6 w-6 mb-1" />
                <span className="text-xs font-medium">{getInitials()}</span>
              </div>
            )}
          </div>
          
          {/* Remove button for existing logo */}
          {previewUrl && !uploading && (
            <button
              onClick={handleRemoveLogo}
              className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 hover:bg-destructive/90 transition-colors"
              title="Remove logo"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex-1 space-y-2">
          <div>
            <Label htmlFor="logo-upload" className="text-sm font-medium">
              Business Logo
            </Label>
            <p className="text-xs text-muted-foreground">
              Upload a logo to represent your business
            </p>
          </div>

          <div className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={triggerFileInput}
              disabled={uploading}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>{uploading ? 'Uploading...' : 'Upload Logo'}</span>
            </Button>

            {previewUrl && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveLogo}
                disabled={uploading}
              >
                Remove
              </Button>
            )}
          </div>

          <p className="text-xs text-muted-foreground">
            Supports JPEG, PNG, WebP, GIF. Max size: 5MB
          </p>
        </div>
      </div>

      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        onChange={handleFileSelect}
        className="hidden"
        disabled={uploading}
      />

      {/* Upload Progress/Status */}
      {uploading && (
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Uploading logo...</span>
        </div>
      )}
    </div>
  );
};

export default BusinessLogoUpload;

-- Add social handles and additional profile information to profiles table
-- This migration adds social media handles, organization name, bio and location to profiles

-- Add the new social media and profile columns
ALTER TABLE public.profiles 
ADD COLUMN linkedin_url text,
ADD COLUMN twitter_url text,
ADD COLUMN instagram_url text,
ADD COLUMN tiktok_url text,
ADD COLUMN organisation_name text,
ADD COLUMN bio text,
ADD COLUMN location_id UUID REFERENCES public.locations(id);

-- Update the members_view to include the new columns
DROP VIEW IF EXISTS public.members_view;

CREATE VIEW public.members_view AS
SELECT 
  id,
  first_name,
  last_name,
  job_title,
  organisation_name,
  bio,
  sustainability_professional,
  avatar_url,
  linkedin_url,
  twitter_url,
  instagram_url,
  tiktok_url,
  location_id,
  created_at AS joinedat
FROM public.profiles
WHERE profile_visible = true;

-- Grant permissions on the updated view
GRANT ALL ON TABLE public.members_view TO anon;
GRANT ALL ON TABLE public.members_view TO authenticated;
GRANT ALL ON TABLE public.members_view TO service_role;

-- Add comments explaining the new columns
COMMENT ON COLUMN public.profiles.linkedin_url IS 'Users LinkedIn profile URL';
COMMENT ON COLUMN public.profiles.twitter_url IS 'Users Twitter/X profile URL';
COMMENT ON COLUMN public.profiles.instagram_url IS 'Users Instagram profile URL';
COMMENT ON COLUMN public.profiles.tiktok_url IS 'Users TikTok profile URL';
COMMENT ON COLUMN public.profiles.organisation_name IS 'Name of the organization user works for';
COMMENT ON COLUMN public.profiles.bio IS 'Short biography or description of the user';
COMMENT ON COLUMN public.profiles.location_id IS 'Primary location where the user is based';

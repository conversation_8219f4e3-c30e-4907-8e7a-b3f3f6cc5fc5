// Location-related types for the database
export interface Location {
  id: string;
  name: string;
  slug: string;
  type: 'country' | 'region' | 'county';
  parent_id: string | null;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// Business location relationships
export interface BusinessCustomerLocation {
  id: string;
  business_id: string;
  location_id: string;
  created_at: string;
  updated_at: string;
}

// Extended business type with location data
export interface BusinessWithLocations {
  id: string;
  business_name: string;
  headquarters_id: string | null;
  headquarters_name: string | null;
  headquarters_slug: string | null;
  headquarters_path: string | null;
  customer_locations: LocationDetails[] | null;
}

export interface LocationDetails {
  id: string;
  name: string;
  slug: string;
  type: 'country' | 'region' | 'county';
  path: string;
}

// County object for the hierarchy
export interface CountyInfo {
  id: string;
  name: string;
}

// Hierarchical location structure for the accordion component
export interface LocationHierarchy {
  [countryKey: string]: {
    name: string;
    regions: {
      [regionKey: string]: {
        name: string;
        counties: CountyInfo[];
      };
    };
  };
}

// Form data types for business location updates
export interface BusinessLocationUpdate {
  business_id: string;
  headquarters_location_id?: string | null;
  customer_location_ids: string[];
}

// API response types
export interface LocationSearchResult {
  id: string;
  name: string;
  slug: string;
  type: 'country' | 'region' | 'county';
  parent_name?: string;
  full_path: string;
}

// Filter types for business directory
export interface BusinessLocationFilter {
  headquarters_locations?: string[];
  customer_locations?: string[];
}

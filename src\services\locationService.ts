import { supabase } from '../integrations/supabase/client';
import type { 
  Location, 
  BusinessWithLocations, 
  BusinessLocationUpdate,
  LocationSearchResult,
  LocationHierarchy 
} from '../types/location.types';

export class LocationService {
  // Get all locations hierarchically
  static async getAllLocations(): Promise<Location[]> {
    const { data, error } = await supabase
      .from('locations' as any)
      .select('*')
      .order('sort_order');

    if (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }

    return (data || []) as unknown as Location[];
  }

  // Get locations formatted for the accordion component
  static async getLocationHierarchy(): Promise<LocationHierarchy> {
    const locations = await this.getAllLocations();
    const hierarchy: LocationHierarchy = {};

    // Build the hierarchy structure
    const countries = locations.filter(l => l.type === 'country');

    for (const country of countries) {
      const regions = locations.filter(l => l.type === 'region' && l.parent_id === country.id);
      const regionData: { [key: string]: { name: string; counties: { id: string; name: string }[] } } = {};

      for (const region of regions) {
        const counties = locations
          .filter(l => l.type === 'county' && l.parent_id === region.id)
          .map(c => ({ id: c.id, name: c.name }));

        regionData[region.slug] = {
          name: region.name,
          counties
        };
      }

      hierarchy[country.slug] = {
        name: country.name,
        regions: regionData
      };
    }

    return hierarchy;
  }

  // Search locations by name
  static async searchLocations(query: string): Promise<LocationSearchResult[]> {
    const { data, error } = await supabase
      .from('locations' as any)
      .select(`
        id,
        name,
        slug,
        type,
        parent:locations!parent_id(name)
      `)
      .ilike('name', `%${query}%`)
      .limit(20);

    if (error) {
      console.error('Error searching locations:', error);
      throw error;
    }

    // Get full paths for results
    const results: LocationSearchResult[] = [];
    for (const location of (data as any) || []) {
      const { data: pathData } = await (supabase as any)
        .rpc('get_location_path', { location_id: location.id });

      results.push({
        id: location.id,
        name: location.name,
        slug: location.slug,
        type: location.type,
        parent_name: location.parent?.name,
        full_path: pathData || location.name
      });
    }

    return results;
  }

  // Get business with location details
  static async getBusinessWithLocations(businessId: string): Promise<BusinessWithLocations | null> {
    const { data, error } = await supabase
      .from('business_locations_view' as any)
      .select('*')
      .eq('business_id', businessId)
      .single();

    if (error) {
      console.error('Error fetching business locations:', error);
      throw error;
    }

    return data as unknown as BusinessWithLocations;
  }

  // Update business headquarters location
  static async updateBusinessHeadquarters(businessId: string, locationId: string | null): Promise<void> {
    const { error } = await supabase
      .from('businesses' as any)
      .update({ headquarters_location_id: locationId })
      .eq('id', businessId);

    if (error) {
      console.error('Error updating business headquarters:', error);
      throw error;
    }
  }

  // Set business customer locations (replaces all existing)
  static async setBusinessCustomerLocations(businessId: string, locationIds: string[]): Promise<void> {
    const { error } = await (supabase as any)
      .rpc('set_business_customer_locations', {
        p_business_id: businessId,
        p_location_ids: locationIds
      });

    if (error) {
      console.error('Error setting business customer locations:', error);
      throw error;
    }
  }

  // Add customer locations to business
  static async addBusinessCustomerLocations(businessId: string, locationIds: string[]): Promise<void> {
    const { error } = await (supabase as any)
      .rpc('add_business_customer_locations', {
        p_business_id: businessId,
        p_location_ids: locationIds
      });

    if (error) {
      console.error('Error adding business customer locations:', error);
      throw error;
    }
  }

  // Remove customer locations from business
  static async removeBusinessCustomerLocations(businessId: string, locationIds: string[]): Promise<void> {
    const { error } = await (supabase as any)
      .rpc('remove_business_customer_locations', {
        p_business_id: businessId,
        p_location_ids: locationIds
      });

    if (error) {
      console.error('Error removing business customer locations:', error);
      throw error;
    }
  }

  // Update all business location data
  static async updateBusinessLocations(update: BusinessLocationUpdate): Promise<void> {
    // Update headquarters if provided
    if (update.headquarters_location_id !== undefined) {
      await this.updateBusinessHeadquarters(update.business_id, update.headquarters_location_id);
    }

    // Update customer locations
    await this.setBusinessCustomerLocations(update.business_id, update.customer_location_ids);
  }

  // Get businesses by location filters
  static async getBusinessesByLocation(
    headquartersLocationIds?: string[],
    customerLocationIds?: string[]
  ): Promise<BusinessWithLocations[]> {
    let query = supabase.from('business_locations_view' as any).select('*');

    if (headquartersLocationIds && headquartersLocationIds.length > 0) {
      query = query.in('headquarters_id', headquartersLocationIds);
    }

    // For customer locations, we need a more complex query
    if (customerLocationIds && customerLocationIds.length > 0) {
      // This will require joining with business_customer_locations
      const { data: businessIds, error: businessIdsError } = await supabase
        .from('business_customer_locations' as any)
        .select('business_id')
        .in('location_id', customerLocationIds);

      if (businessIdsError) {
        console.error('Error fetching businesses by customer location:', businessIdsError);
        throw businessIdsError;
      }

      const uniqueBusinessIds = [...new Set((businessIds as any)?.map((b: any) => b.business_id) || [])];
      query = query.in('business_id', uniqueBusinessIds);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching businesses by location:', error);
      throw error;
    }

    return (data || []) as unknown as BusinessWithLocations[];
  }

  // Convert location selection keys to location IDs
  static async convertSelectionKeysToIds(selectionKeys: string[]): Promise<string[]> {
    const locationIds: string[] = [];

    console.log('Converting selection keys:', selectionKeys);

    for (const key of selectionKeys) {
      // Parse the selection key format: country::region::county::id
      const parts = key.split('::');
      if (parts.length >= 4) {
        // Last part is the location ID
        const locationId = parts[3];

        console.log('Extracting location ID from key:', locationId);

        // Verify the location exists
        const { data, error } = await supabase
          .from('locations' as any)
          .select('id, name, type')
          .eq('id', locationId)
          .eq('type', 'county')
          .single();

        if (error || !data) {
          console.log('Location not found for ID:', locationId);
          continue;
        }

        console.log('Found location:', data);
        locationIds.push(locationId);
      } else {
        console.log('Invalid key format (not enough parts):', key);
      }
    }

    console.log('Final location IDs:', locationIds);
    return locationIds;
  }

  // Convert location IDs to accordion selection keys
  static async convertIdsToSelectionKeys(locationIds: string[]): Promise<string[]> {
    console.log('Converting IDs to selection keys:', locationIds);
    const selectionKeys: string[] = [];

    for (const locationId of locationIds) {
      try {
        // Get the location details
        const { data: location, error } = await supabase
          .from('locations' as any)
          .select('id, name, slug, type, parent_id')
          .eq('id', locationId)
          .single();

        if (error || !location) {
          console.error('Error finding location for ID:', locationId, error);
          continue;
        }

        if ((location as any).type === 'county' && (location as any).parent_id) {
          // Get the region
          const { data: region } = await supabase
            .from('locations' as any)
            .select('slug, parent_id')
            .eq('id', (location as any).parent_id)
            .single();

          if (region && (region as any).parent_id) {
            // Get the country
            const { data: country } = await supabase
              .from('locations' as any)
              .select('slug')
              .eq('id', (region as any).parent_id)
              .single();

            if (country) {
              // Generate the key in the same format as LocationAccordion
              const countySlug = (location as any).name.toLowerCase().replace(/[^a-z0-9]/g, '-');
              const selectionKey = `${(country as any).slug}::${(region as any).slug}::${countySlug}::${(location as any).id}`;
              console.log('Generated selection key:', selectionKey);
              selectionKeys.push(selectionKey);
            }
          }
        }
      } catch (error) {
        console.error('Error converting location ID to selection key:', locationId, error);
      }
    }

    console.log('Final selection keys:', selectionKeys);
    return selectionKeys;
  }
}

export interface AvatarUploadResult {
  url: string;
  key: string;
}

export interface AvatarValidationError {
  message: string;
  code: 'FILE_TOO_LARGE' | 'INVALID_TYPE' | 'INVALID_EXTENSION' | 'UPLOAD_FAILED' | 'DELETE_FAILED';
}

export interface AvatarUploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}

export interface AvatarMetadata {
  userId: string;
  originalName: string;
  uploadedAt: string;
  size: number;
  type: string;
}

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Users, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import MembersList, { MemberItem } from "../components/MembersList";
import { Database } from "@/types/database.types";

type Profile = Database['public']['Tables']['profiles']['Row'];

const MembersPage = () => {
  const { user } = useAuth();
  const [members, setMembers] = useState<MemberItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMembers = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, job_title, created_at, sustainability_professional, avatar_url, organisation_name, location_id')
          .eq('profile_visible', true);

        if (profilesError) {
          throw profilesError;
        }

        let locationMap: Record<string, string> = {};
        const locationIds = Array.from(new Set((profiles || []).map((p) => p.location_id).filter(Boolean)));
        if (locationIds.length > 0) {
          const { data: locations, error: locationsError } = await supabase
            .from('locations')
            .select('id, name')
            .in('id', locationIds);
          if (!locationsError && locations) {
            locationMap = Object.fromEntries(locations.map((loc) => [loc.id, loc.name]));
          }
        }

        if (profiles) {
          const mappedMembers = profiles.map((profile): MemberItem => ({
            id: profile.id,
            first_name: profile.first_name,
            last_name: profile.last_name,
            job_title: profile.job_title,
            joinedAt: profile.created_at || new Date().toISOString(),
            avatar_url: profile.avatar_url,
            sustainability_professional: profile.sustainability_professional,
            organisation_name: profile.organisation_name || '',
            location_id: profile.location_id,
            location_name: profile.location_id ? locationMap[profile.location_id] || '' : ''
          }));

          setMembers(mappedMembers);
        }
      } catch (err) {
        console.error("Error fetching members:", err);
        setError("Failed to load members. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [user]);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Access Restricted</h1>
          <p className="text-muted-foreground">Please sign in to view the members directory.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <Users className="w-8 h-8" />
          Members Directory
        </h1>
        <p className="text-muted-foreground">
          Connect with other members of our Net Zero community.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin" />
          <span className="ml-2">Loading members...</span>
        </div>
      ) : error ? (
        <div className="p-4 bg-destructive/10 text-destructive rounded-lg">
          {error}
        </div>
      ) : (
        <MembersList members={members} />
      )}

      <div className="mt-8 p-4 bg-muted rounded-lg border">
        <p className="text-sm text-muted-foreground">
          <strong>Note:</strong> This directory shows all members with profiles in the Net Zero Nexus Hub.
          {members.length <= 1 && " Currently displaying limited data while the community grows."}
        </p>
      </div>
    </div>
  );
};

export default MembersPage;

-- Add avatar support to profiles table
-- This migration adds avatar_url and avatar_cloudflare_key columns

-- Add the new avatar columns
ALTER TABLE public.profiles 
ADD COLUMN avatar_url text,
ADD COLUMN avatar_cloudflare_key text;

-- Update the members_view to include avatar
DROP VIEW IF EXISTS public.members_view;

CREATE VIEW public.members_view AS
SELECT 
  id,
  first_name,
  last_name,
  job_title,
  sustainability_professional,
  avatar_url,
  created_at AS joinedat
FROM public.profiles;

-- Grant permissions on the updated view
GRANT ALL ON TABLE public.members_view TO anon;
GRANT ALL ON TABLE public.members_view TO authenticated;
GRANT ALL ON TABLE public.members_view TO service_role;

-- Create avatar cleanup queue table for handling orphaned images
CREATE TABLE IF NOT EXISTS public.avatar_cleanup_queue (
  id SERIAL PRIMARY KEY,
  cloudflare_key TEXT NOT NULL,
  deleted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed BOOLEAN DEFAULT FALSE
);

-- Grant permissions on the cleanup queue table
GRANT ALL ON TABLE public.avatar_cleanup_queue TO authenticated;
GRANT ALL ON TABLE public.avatar_cleanup_queue TO service_role;

-- Function to handle avatar cleanup on profile deletion
CREATE OR REPLACE FUNCTION public.cleanup_profile_assets()
RETURNS TRIGGER AS $$
BEGIN
  -- Store the avatar key for external cleanup if it exists
  IF OLD.avatar_cloudflare_key IS NOT NULL THEN
    INSERT INTO public.avatar_cleanup_queue (cloudflare_key, deleted_at)
    VALUES (OLD.avatar_cloudflare_key, NOW());
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for profile deletion cleanup
DROP TRIGGER IF EXISTS profile_deletion_cleanup ON public.profiles;
CREATE TRIGGER profile_deletion_cleanup
  BEFORE DELETE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.cleanup_profile_assets();

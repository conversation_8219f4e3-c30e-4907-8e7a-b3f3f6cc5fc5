// UK Industry Service for managing industries and user/business associations
import { supabase } from '@/integrations/supabase/client';
import type {
  UKIndustry,
  UKIndustryWithChildren,
  UKIndustryWithParent,
  ProfileIndustry,
  BusinessIndustry,
  BusinessTargetIndustry,
  NewProfileIndustry,
  NewBusinessIndustry,
  NewBusinessTargetIndustry,
  IndustryHierarchy,
  UserIndustryResponse,
  BusinessIndustriesResponse,
  IndustrySearchResult
} from '@/types/uk-industries.types';

export class UKIndustryService {
  /**
   * Fetch all industries with their children (hierarchical structure)
   */
  static async getAllIndustriesWithChildren(): Promise<UKIndustryWithChildren[]> {
    const { data: industries, error } = await supabase
      .from('uk_industries')
      .select('*')
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch industries: ${error.message}`);
    }

    // Group children under their parents
    const parentIndustries = industries.filter(industry => !industry.parent_id);
    const childIndustries = industries.filter(industry => industry.parent_id);

    const industriesWithChildren: UKIndustryWithChildren[] = parentIndustries.map(parent => ({
      ...parent,
      children: childIndustries
        .filter(child => child.parent_id === parent.id)
        .sort((a, b) => a.sort_order - b.sort_order)
    }));

    return industriesWithChildren.sort((a, b) => a.sort_order - b.sort_order);
  }

  /**
   * Get industries as a hierarchical structure for UI components
   */
  static async getIndustryHierarchy(): Promise<IndustryHierarchy> {
    const industriesWithChildren = await this.getAllIndustriesWithChildren();
    
    const hierarchy: IndustryHierarchy = {};
    
    industriesWithChildren.forEach(parent => {
      hierarchy[parent.id] = {
        id: parent.id,
        name: parent.name,
        description: parent.description,
        sort_order: parent.sort_order,
        children: {}
      };
      
      parent.children.forEach(child => {
        hierarchy[parent.id].children[child.id] = {
          id: child.id,
          name: child.name,
          description: child.description,
          sort_order: child.sort_order
        };
      });
    });
    
    return hierarchy;
  }

  /**
   * Get all child industries (for selection components)
   */
  static async getChildIndustries(): Promise<UKIndustryWithParent[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .not('parent_id', 'is', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch child industries: ${error.message}`);
    }

    return data.map(item => ({
      ...item,
      parent: item.parent as UKIndustry
    }));
  }

  /**
   * Get user's industry
   */
  static async getUserIndustry(userId: string): Promise<UserIndustryResponse> {
    const { data, error } = await supabase
      .from('profile_industry')
      .select(`
        industry_id,
        uk_industries (
          *,
          parent:uk_industries!uk_industries_parent_id_fkey (*)
        )
      `)
      .eq('profile_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return { industry: null }; // No industry set
      }
      throw new Error(`Failed to fetch user industry: ${error.message}`);
    }

    const industry = data.uk_industries as any;
    return {
      industry: {
        ...industry,
        parent: industry.parent
      }
    };
  }

  /**
   * Update user's industry (single select)
   */
  static async updateUserIndustry(userId: string, industryId: string | null): Promise<void> {
    // First, delete existing industry
    const { error: deleteError } = await supabase
      .from('profile_industry')
      .delete()
      .eq('profile_id', userId);

    if (deleteError) {
      throw new Error(`Failed to delete existing industry: ${deleteError.message}`);
    }

    // Then, insert new industry if provided
    if (industryId) {
      const newIndustry: NewProfileIndustry = {
        profile_id: userId,
        industry_id: industryId
      };

      const { error: insertError } = await supabase
        .from('profile_industry')
        .insert(newIndustry);

      if (insertError) {
        throw new Error(`Failed to insert new industry: ${insertError.message}`);
      }
    }
  }

  /**
   * Get business's industries (main industry + target industries)
   */
  static async getBusinessIndustries(businessId: string): Promise<BusinessIndustriesResponse> {
    // Get main industry
    const { data: mainIndustryData, error: mainError } = await supabase
      .from('business_industry')
      .select(`
        industry_id,
        uk_industries (
          *,
          parent:uk_industries!uk_industries_parent_id_fkey (*)
        )
      `)
      .eq('business_id', businessId)
      .single();

    let main_industry = null;
    if (!mainError && mainIndustryData) {
      const industry = mainIndustryData.uk_industries as any;
      main_industry = {
        ...industry,
        parent: industry.parent
      };
    }

    // Get target industries
    const { data: targetIndustriesData, error: targetError } = await supabase
      .from('business_target_industries')
      .select(`
        industry_id,
        uk_industries (
          *,
          parent:uk_industries!uk_industries_parent_id_fkey (*)
        )
      `)
      .eq('business_id', businessId);

    if (targetError) {
      throw new Error(`Failed to fetch target industries: ${targetError.message}`);
    }

    const target_industries = targetIndustriesData.map(item => {
      const industry = item.uk_industries as any;
      return {
        ...industry,
        parent: industry.parent
      };
    });

    return { main_industry, target_industries };
  }

  /**
   * Update business's main industry (single select)
   */
  static async updateBusinessMainIndustry(businessId: string, industryId: string | null): Promise<void> {
    // First, delete existing main industry
    const { error: deleteError } = await supabase
      .from('business_industry')
      .delete()
      .eq('business_id', businessId);

    if (deleteError) {
      throw new Error(`Failed to delete existing main industry: ${deleteError.message}`);
    }

    // Then, insert new main industry if provided
    if (industryId) {
      const newIndustry: NewBusinessIndustry = {
        business_id: businessId,
        industry_id: industryId
      };

      const { error: insertError } = await supabase
        .from('business_industry')
        .insert(newIndustry);

      if (insertError) {
        throw new Error(`Failed to insert new main industry: ${insertError.message}`);
      }
    }
  }

  /**
   * Update business's target industries (multi-select)
   */
  static async updateBusinessTargetIndustries(businessId: string, industryIds: string[]): Promise<void> {
    // First, delete existing target industries
    const { error: deleteError } = await supabase
      .from('business_target_industries')
      .delete()
      .eq('business_id', businessId);

    if (deleteError) {
      throw new Error(`Failed to delete existing target industries: ${deleteError.message}`);
    }

    // Then, insert new target industries if provided
    if (industryIds.length > 0) {
      const newTargetIndustries: NewBusinessTargetIndustry[] = industryIds.map(industryId => ({
        business_id: businessId,
        industry_id: industryId
      }));

      const { error: insertError } = await supabase
        .from('business_target_industries')
        .insert(newTargetIndustries);

      if (insertError) {
        throw new Error(`Failed to insert new target industries: ${insertError.message}`);
      }
    }
  }

  /**
   * Get industry details by ID
   */
  static async getIndustryById(industryId: string): Promise<UKIndustryWithParent | null> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .eq('id', industryId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Not found
      }
      throw new Error(`Failed to fetch industry: ${error.message}`);
    }

    return {
      ...data,
      parent: data.parent as UKIndustry | null
    };
  }

  /**
   * Search industries by name or description
   */
  static async searchIndustries(query: string): Promise<IndustrySearchResult[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select(`
        *,
        parent:uk_industries!uk_industries_parent_id_fkey (*)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .order('name');

    if (error) {
      throw new Error(`Failed to search industries: ${error.message}`);
    }

    return data.map(item => ({
      industry: {
        ...item,
        parent: item.parent as UKIndustry | null
      },
      matchType: item.name.toLowerCase().includes(query.toLowerCase()) ? 'name' : 'description'
    }));
  }

  /**
   * Get parent industries only
   */
  static async getParentIndustries(): Promise<UKIndustry[]> {
    const { data, error } = await supabase
      .from('uk_industries')
      .select('*')
      .is('parent_id', null)
      .order('sort_order');

    if (error) {
      throw new Error(`Failed to fetch parent industries: ${error.message}`);
    }

    return data;
  }
}

-- Add is_primary column to profile_netzero_interests
ALTER TABLE profile_netzero_interests
ADD COLUMN is_primary boolean DEFAULT false;

-- Ensure only one primary per profile (optional, for data integrity)
CREATE OR REPLACE FUNCTION enforce_single_primary_profile_netzero()
RET<PERSON>NS TRIGGER AS $$
BEGIN
  IF NEW.is_primary THEN
    UPDATE profile_netzero_interests
    SET is_primary = false
    WHERE profile_id = NEW.profile_id AND id <> NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_single_primary_profile_netzero ON profile_netzero_interests;
CREATE TRIGGER trg_single_primary_profile_netzero
BEFORE INSERT OR UPDATE ON profile_netzero_interests
FOR EACH ROW EXECUTE FUNCTION enforce_single_primary_profile_netzero();

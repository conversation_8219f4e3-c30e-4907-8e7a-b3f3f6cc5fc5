import React from 'react';
import { Card, CardHeader } from '@/components/ui/card';
import { Business } from '../types';

interface BusinessCardProps {
  business: Business;
}

export const BusinessCard: React.FC<BusinessCardProps> = ({ business }) => {
  return (
    <Card className="h-full">
      <CardHeader>
        <h3 className="text-lg font-semibold">{business.business_name}</h3>
        <div className="text-sm text-muted-foreground space-y-1">
          {business.city && <p>{business.city}</p>}
          {business.website && (
            <p>
              <a 
                href={business.website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                Visit Website
              </a>
            </p>
          )}
          {business.contact_email && (
            <p>
              <a 
                href={`mailto:${business.contact_email}`}
                className="text-primary hover:underline"
              >
                Contact Business
              </a>
            </p>
          )}
        </div>
      </CardHeader>
    </Card>
  );
};
